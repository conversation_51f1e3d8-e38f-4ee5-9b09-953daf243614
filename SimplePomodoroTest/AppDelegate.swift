import Cocoa

func writeDebugLog(_ message: String) {
    let logMessage = "\(Date()): \(message)\n"
    let logPath = "/tmp/uProd_debug.log"
    if let data = logMessage.data(using: .utf8) {
        if FileManager.default.fileExists(atPath: logPath) {
            if let fileHandle = FileHandle(forWritingAtPath: logPath) {
                fileHandle.seekToEndOfFile()
                fileHandle.write(data)
                fileHandle.closeFile()
            }
        } else {
            try? data.write(to: URL(fileURLWithPath: logPath))
        }
    }
}

class AppDelegate: NSObject, NSApplicationDelegate {

    var statusItem: NSStatusItem!
    var pomodoroTimer: PomodoroTimer!
    var notificationWindow: IntervalNotificationWindow?
    var settingsWindow: SettingsWindow?
    var statisticsManager: StatisticsManager!
    var statisticsWindow: StatisticsWindow?
    var workPatternAnalyzer: WorkPatternAnalyzer!
    var demoDataManager: DemoDataManager!
    var projectManager: ProjectManager!
    var projectManagementWindow: ProjectManagementWindow?

    var motivationManager: MotivationManager!

    // Текущий выбранный проект для запуска интервала
    var currentProjectId: UUID?

    func applicationDidFinishLaunching(_ aNotification: Notification) {
        NSLog("🚀 uProd: App starting...")

        // Скрываем из Dock
        NSApp.setActivationPolicy(.accessory)
        NSLog("🚀 uProd: Set activation policy to accessory")

        // Инициализируем автозапуск
        LaunchAtLoginManager.shared.initializeOnFirstLaunch()
        NSLog("🚀 uProd: Launch at login initialized")

        // Инициализируем статистику
        setupStatistics()
        NSLog("🚀 uProd: Statistics setup complete")

        // Инициализируем таймер
        setupPomodoroTimer()
        NSLog("🚀 uProd: Timer setup complete")

        // Создаём menu bar item
        setupStatusItem()
        NSLog("🚀 uProd: Status item setup complete")

        NSLog("🚀 uProd: App fully initialized")
    }

    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        // Не завершаем приложение при закрытии последнего окна
        return false
    }

    func applicationSupportsSecureRestorableState(_ app: NSApplication) -> Bool {
        return true
    }

    private func setupStatistics() {
        statisticsManager = StatisticsManager()
        projectManager = ProjectManager()
        workPatternAnalyzer = WorkPatternAnalyzer(statisticsManager: statisticsManager)
        motivationManager = MotivationManager(statisticsManager: statisticsManager, analyzer: workPatternAnalyzer)
        demoDataManager = DemoDataManager(statisticsManager: statisticsManager)
    }

    private func setupPomodoroTimer() {
        pomodoroTimer = PomodoroTimer()

        // Настраиваем колбэки
        pomodoroTimer.onStateChanged = { [weak self] state in
            DispatchQueue.main.async {
                self?.updateStatusItem()
                self?.updateMenu()
            }
        }

        pomodoroTimer.onTimeUpdate = { [weak self] timeRemaining, overtimeElapsed in
            DispatchQueue.main.async {
                self?.updateStatusItem()
            }
        }

        pomodoroTimer.onIntervalCompleted = { [weak self] in
            DispatchQueue.main.async {
                self?.showCompletionWindow()
            }
        }

        pomodoroTimer.onReminderTriggered = { [weak self] reminderCount in
            DispatchQueue.main.async {
                self?.showReminderWindow(reminderCount: reminderCount)
            }
        }

        pomodoroTimer.onFullIntervalCompleted = { [weak self] duration in
            DispatchQueue.main.async {
                self?.statisticsManager.recordCompletedInterval(duration: duration, projectId: self?.currentProjectId)
                self?.motivationManager.checkAndSendMotivationalNotification()
                // Сбрасываем текущий проект после завершения интервала
                self?.currentProjectId = nil
            }
        }
    }

    private func setupStatusItem() {
        NSLog("🚀 uProd: Creating status item...")
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.variableLength)

        if let button = statusItem.button {
            button.title = pomodoroTimer.getStatusText()
            button.toolTip = "uProd - Productivity Timer"
            button.action = #selector(statusItemClicked)
            button.target = self
            NSLog("🚀 uProd: Status item button configured with title: \(button.title)")
        } else {
            NSLog("❌ uProd: Failed to get status item button")
        }

        // Создаем меню
        writeDebugLog("🔧 AppDelegate: setupStatusItem вызывает updateMenu")
        updateMenu()
        writeDebugLog("🔧 AppDelegate: setupStatusItem updateMenu завершен")

        // Подписываемся на уведомления об изменении избранных
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(favoritesChanged),
            name: NSNotification.Name("FavoritesChanged"),
            object: nil
        )

        NSLog("🚀 uProd: Status item setup complete")
    }

    @objc private func statusItemClicked() {
        // При клике показываем меню
        // Меню автоматически показывается при клике, если оно установлено
    }

    private func updateStatusItem() {
        // Получаем название проекта если есть
        var projectName: String? = nil
        if let projectId = currentProjectId,
           let project = projectManager.getProject(by: projectId) {
            projectName = project.effectiveEmoji
        }

        let statusText = pomodoroTimer.getStatusText(projectName: projectName)

        // Проверяем, нужен ли красный фон (с 10 минут переработки)
        let needsRedBackground = pomodoroTimer.state == .overtime && pomodoroTimer.overtimeElapsed >= 600 // 10 минут = 600 секунд

        if needsRedBackground {
            // Создаем изображение с красным фоном и белым текстом
            let image = createRedBackgroundImage(with: statusText)
            statusItem.button?.image = image
            statusItem.button?.title = ""
            statusItem.button?.attributedTitle = NSAttributedString(string: "")
        } else {
            // Обычный цветной текст без фона
            statusItem.button?.image = nil

            let textColor: NSColor
            if pomodoroTimer.state == .overtime {
                textColor = getOvertimeTextColor()
            } else {
                textColor = NSColor.controlTextColor
            }

            // Полностью очищаем button
            statusItem.button?.title = ""
            statusItem.button?.attributedTitle = NSAttributedString(string: "")

            // Создаем attributed string с цветом для каждого символа
            let attributedString = NSMutableAttributedString()
            let font = NSFont.monospacedSystemFont(ofSize: 13, weight: .medium)

            // Добавляем каждый символ отдельно с принудительным цветом
            for char in statusText {
                let charString = String(char)
                let charAttributed = NSAttributedString(string: charString, attributes: [
                    .foregroundColor: textColor,
                    .font: font
                ])
                attributedString.append(charAttributed)
            }

            // Устанавливаем с принудительным обновлением
            statusItem.button?.attributedTitle = attributedString

            // Принудительно перерисовываем весь status item
            if let button = statusItem.button {
                button.setNeedsDisplay(button.bounds)
            }
        }
    }

    private func getOvertimeTextColor() -> NSColor {
        let minutes = Int(pomodoroTimer.overtimeElapsed) / 60

        switch minutes {
        case 0:
            return NSColor.controlTextColor  // Первая минута (0-59 сек) - обычный цвет
        case 1..<3:
            return NSColor.systemYellow      // 1-2 минуты - желтый
        case 3..<5:
            return NSColor.systemOrange      // 3-4 минуты - оранжевый
        case 5..<10:
            return NSColor.systemRed         // 5-9 минут - красный
        case 10..<15:
            return NSColor(red: 0.7, green: 0.1, blue: 0.1, alpha: 1.0)  // 10-14 минут - темно-бордовый/темно-красный
        default:
            return NSColor(red: 0.7, green: 0.1, blue: 0.1, alpha: 1.0)  // 15+ минут - тот же темно-бордовый (смайлик меняется в getOvertimeIcon)
        }
    }

    private func createRedBackgroundImage(with text: String) -> NSImage {
        let font = NSFont.monospacedSystemFont(ofSize: 13, weight: .medium)
        let textAttributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: NSColor.white
        ]

        // Измеряем размер текста
        let textSize = text.size(withAttributes: textAttributes)

        // Делаем фон на всю высоту status bar (обычно 22px) с отступами слева-справа
        let statusBarHeight: CGFloat = 22
        let horizontalPadding: CGFloat = 6
        let imageSize = NSSize(width: textSize.width + horizontalPadding * 2, height: statusBarHeight)

        // Создаем изображение
        let image = NSImage(size: imageSize)
        image.lockFocus()

        // Рисуем красный фон на всю высоту
        let backgroundRect = NSRect(origin: .zero, size: imageSize)
        NSColor.systemRed.setFill()
        backgroundRect.fill()

        // Рисуем белый текст по центру
        let textRect = NSRect(
            x: horizontalPadding,
            y: (imageSize.height - textSize.height) / 2,
            width: textSize.width,
            height: textSize.height
        )
        text.draw(in: textRect, withAttributes: textAttributes)

        image.unlockFocus()

        // НЕ устанавливаем template - чтобы сохранить красный цвет
        image.isTemplate = false

        return image
    }

    private func updateMenu() {
        writeDebugLog("🔧 AppDelegate: updateMenu вызван")
        let menu = NSMenu()

        if pomodoroTimer.isActive() {
            // Показываем информацию о текущем состоянии
            let statusItem = NSMenuItem(title: getStatusMenuText(), action: nil, keyEquivalent: "")
            statusItem.isEnabled = false
            menu.addItem(statusItem)

            menu.addItem(NSMenuItem.separator())

            // Кнопка завершения интервала
            let stopItem = NSMenuItem(title: "Завершить интервал", action: #selector(stopInterval), keyEquivalent: "")
            stopItem.target = self
            menu.addItem(stopItem)
        } else {
            // Избранные проекты для быстрого запуска
            let favoriteProjects = projectManager.getFavoriteProjects()
            let minutes = Int(PomodoroTimer.workDuration / 60)

            if favoriteProjects.isEmpty {
                // Если нет избранных проектов, показываем обычную кнопку
                let startItem = NSMenuItem(title: "Начать интервал (\(minutes) мин)", action: #selector(startInterval), keyEquivalent: "")
                startItem.target = self
                menu.addItem(startItem)
            } else {
                // Показываем кнопки для каждого избранного проекта
                for (index, project) in favoriteProjects.enumerated() {
                    let colorIndicator = createColorIndicator(for: project)
                    let title = "\(colorIndicator) Начать: \(project.name) (\(minutes) мин)"
                    let selector = #selector(startIntervalWithProject(_:))
                    let keyEquivalent = index < 3 ? "\(index + 1)" : ""

                    let projectItem = NSMenuItem(title: title, action: selector, keyEquivalent: keyEquivalent)
                    projectItem.target = self
                    projectItem.representedObject = project.id
                    menu.addItem(projectItem)
                }
            }

            menu.addItem(NSMenuItem.separator())

            // Подменю "Другие проекты"
            let otherProjectsItem = NSMenuItem(title: "Другие проекты", action: nil, keyEquivalent: "")
            let otherProjectsMenu = createOtherProjectsMenu()
            otherProjectsItem.submenu = otherProjectsMenu
            menu.addItem(otherProjectsItem)

            // Кнопка тестового запуска
            let testItem = NSMenuItem(title: "Тестовый запуск (3 сек)", action: #selector(startTestInterval), keyEquivalent: "t")
            testItem.target = self
            menu.addItem(testItem)
        }

        menu.addItem(NSMenuItem.separator())

        // Кнопка управления проектами
        let projectsItem = NSMenuItem(title: "Управление проектами...", action: #selector(showProjectManagement), keyEquivalent: "p")
        projectsItem.target = self
        menu.addItem(projectsItem)
        writeDebugLog("🔧 AppDelegate: добавлен пункт меню 'Управление проектами...', target: \(String(describing: projectsItem.target)), action: \(String(describing: projectsItem.action))")

        // Кнопка настроек
        let settingsItem = NSMenuItem(title: "Настройки...", action: #selector(showSettings), keyEquivalent: ",")
        settingsItem.target = self
        menu.addItem(settingsItem)

        // Кнопка статистики
        let statisticsItem = NSMenuItem(title: "Статистика...", action: #selector(showStatistics), keyEquivalent: "s")
        statisticsItem.target = self
        menu.addItem(statisticsItem)

        // Кнопки для работы с демо данными
        let createDemoDataItem = NSMenuItem(title: "Создать демо данные", action: #selector(createDemoData), keyEquivalent: "")
        createDemoDataItem.target = self
        menu.addItem(createDemoDataItem)

        let clearDataItem = NSMenuItem(title: "Очистить все данные", action: #selector(clearAllData), keyEquivalent: "")
        clearDataItem.target = self
        menu.addItem(clearDataItem)

        let showDemoDescriptionItem = NSMenuItem(title: "Описание демо данных", action: #selector(showDemoDescription), keyEquivalent: "")
        showDemoDescriptionItem.target = self
        menu.addItem(showDemoDescriptionItem)

        menu.addItem(NSMenuItem.separator())

        // Кнопка выхода
        let quitItem = NSMenuItem(title: "Выход", action: #selector(quitApp), keyEquivalent: "q")
        quitItem.target = self
        menu.addItem(quitItem)

        statusItem.menu = menu
    }

    private func getStatusMenuText() -> String {
        switch pomodoroTimer.state {
        case .idle:
            return "Готов к работе"
        case .working:
            let timeText = "Работаем: \(pomodoroTimer.formatTime(pomodoroTimer.timeRemaining))"
            if let projectId = currentProjectId,
               let project = projectManager.getProject(by: projectId) {
                return "\(project.effectiveEmoji) \(project.name) - \(timeText)"
            }
            return timeText
        case .overtime:
            let timeText = "Переработка: +\(pomodoroTimer.formatTime(pomodoroTimer.overtimeElapsed))"
            if let projectId = currentProjectId,
               let project = projectManager.getProject(by: projectId) {
                return "\(project.effectiveEmoji) \(project.name) - \(timeText)"
            }
            return timeText
        }
    }

    @objc private func startInterval() {
        currentProjectId = nil
        pomodoroTimer.startInterval()
    }

    @objc private func startIntervalWithProject(_ sender: NSMenuItem) {
        if let projectId = sender.representedObject as? UUID {
            currentProjectId = projectId
            projectManager.markProjectAsUsed(projectId)
            pomodoroTimer.startInterval()
        }
    }

    @objc private func startTestInterval() {
        // Запускаем тестовый интервал на 3 секунды
        currentProjectId = nil
        pomodoroTimer.startInterval(testDuration: 3)
    }

    @objc private func stopInterval() {
        pomodoroTimer.stopInterval()
    }

    @objc private func showProjectManagement() {
        NSLog("🔧 AppDelegate: showProjectManagement вызван")
        if projectManagementWindow == nil {
            NSLog("🔧 AppDelegate: создаем новый ProjectManagementWindow")
            projectManagementWindow = ProjectManagementWindow(projectManager: projectManager)
        }
        NSLog("🔧 AppDelegate: вызываем showWindow")
        projectManagementWindow?.showWindow()
        NSLog("🔧 AppDelegate: showProjectManagement завершен")
    }

    // MARK: - Project Menu Creation

    private func createOtherProjectsMenu() -> NSMenu {
        let menu = NSMenu()
        let activeProjects = projectManager.getActiveProjects()
        let favoriteProjects = projectManager.getFavoriteProjects()
        let favoriteIds = Set(favoriteProjects.map { $0.id })

        // Группируем проекты по типам
        let projectsByType = Dictionary(grouping: activeProjects) { $0.type }
        let minutes = Int(PomodoroTimer.workDuration / 60)

        for projectType in ProjectType.allCases {
            if let projects = projectsByType[projectType], !projects.isEmpty {
                // Добавляем заголовок типа
                let typeHeader = NSMenuItem(title: projectType.displayName, action: nil, keyEquivalent: "")
                typeHeader.isEnabled = false
                menu.addItem(typeHeader)

                // Добавляем проекты этого типа
                for project in projects.sorted(by: { $0.name < $1.name }) {
                    // Пропускаем проекты, которые уже в избранном
                    if favoriteIds.contains(project.id) { continue }

                    let colorIndicator = createColorIndicator(for: project)
                    let title = "  \(colorIndicator) \(project.name) (\(minutes) мин)"
                    let projectItem = NSMenuItem(title: title, action: #selector(startIntervalWithProject(_:)), keyEquivalent: "")
                    projectItem.target = self
                    projectItem.representedObject = project.id
                    menu.addItem(projectItem)
                }

                menu.addItem(NSMenuItem.separator())
            }
        }

        // Удаляем последний разделитель если он есть
        if menu.items.last?.isSeparatorItem == true {
            menu.removeItem(menu.items.last!)
        }

        return menu
    }

    // MARK: - Color Indicator Helper

    private func createColorIndicator(for project: Project) -> String {
        // Используем только эмодзи типа проекта, без дублирования
        return project.type.emoji
    }

    @objc private func showSettings() {
        if settingsWindow == nil {
            settingsWindow = SettingsWindow()
            settingsWindow?.setOnSettingsChanged { [weak self] newDuration in
                self?.pomodoroTimer.updateWorkDuration(newDuration)
                self?.updateStatusItem()
                self?.updateMenu()
            }
        }
        settingsWindow?.updateIntervalDuration(PomodoroTimer.workDuration)
        settingsWindow?.showWindow()
    }

    @objc private func showStatistics() {
        if statisticsWindow == nil {
            statisticsWindow = StatisticsWindow(statisticsManager: statisticsManager, motivationManager: motivationManager)
        }
        statisticsWindow?.showWindow()
    }

    @objc private func createDemoData() {
        demoDataManager.createDemoData()
        updateStatusItem()
        updateMenu()

        // Если окно статистики открыто, обновляем его
        statisticsWindow?.refreshStatistics()

        // Показываем уведомление
        showAlert(title: "Демо данные созданы", message: "Созданы демо данные с различными проблемами по неделям. Откройте статистику для просмотра.")
    }

    @objc private func clearAllData() {
        // Показываем подтверждение
        let alert = NSAlert()
        alert.messageText = "Очистить все данные?"
        alert.informativeText = "Это действие удалит все записанные интервалы. Отменить это действие будет невозможно."
        alert.addButton(withTitle: "Очистить")
        alert.addButton(withTitle: "Отмена")
        alert.alertStyle = .warning

        let response = alert.runModal()
        if response == .alertFirstButtonReturn {
            statisticsManager.clearAllIntervals()
            updateStatusItem()
            updateMenu()

            // Если окно статистики открыто, обновляем его
            statisticsWindow?.refreshStatistics()

            showAlert(title: "Данные очищены", message: "Все интервалы были удалены.")
        }
    }

    @objc private func showDemoDescription() {
        let description = demoDataManager.getDemoDataDescription()
        showAlert(title: "Описание демо данных", message: description)
    }

    private func showAlert(title: String, message: String) {
        let alert = NSAlert()
        alert.messageText = title
        alert.informativeText = message
        alert.addButton(withTitle: "OK")
        alert.runModal()
    }

    @objc private func quitApp() {
        NSApplication.shared.terminate(nil)
    }

    @objc private func favoritesChanged() {
        // Обновляем меню при изменении избранных проектов
        DispatchQueue.main.async { [weak self] in
            self?.updateMenu()
        }
    }

    // MARK: - Notification Window Methods

    private func showCompletionWindow() {
        print("🎨 AppDelegate: Показ красивого окна завершения")

        // Закрываем предыдущее окно если оно есть
        if let existingWindow = notificationWindow {
            existingWindow.orderOut(nil)
            notificationWindow = nil
        }

        notificationWindow = IntervalNotificationWindow()

        // Позиционируем окно относительно status item
        if let button = statusItem.button, let window = button.window {
            let buttonFrame = button.convert(button.bounds, to: nil)
            let screenFrame = window.convertToScreen(buttonFrame)
            notificationWindow?.positionRelativeToStatusItem(statusItemFrame: screenFrame)
        }

        notificationWindow?.setCallbacks(
            onComplete: { [weak self] in
                print("🎨 AppDelegate: Колбэк завершения интервала")
                self?.pomodoroTimer.completeInterval()
                self?.notificationWindow = nil
            },
            onExtend1: { [weak self] in
                print("🎨 AppDelegate: Колбэк продления на 1 минуту")
                self?.pomodoroTimer.extendInterval(minutes: 1)
                self?.notificationWindow = nil
            },
            onExtend5: { [weak self] in
                print("🎨 AppDelegate: Колбэк продления на 5 минут")
                self?.pomodoroTimer.extendInterval(minutes: 5)
                self?.notificationWindow = nil
            }
        )
        notificationWindow?.showWindow()
    }

    private func showReminderWindow(reminderCount: Int) {
        print("🎨 AppDelegate: Показ напоминания #\(reminderCount)")

        // Если окно уже открыто, обновляем сообщение
        if let window = notificationWindow {
            window.updateMessage(reminderCount: reminderCount)
            // Убеждаемся что окно видимо
            window.makeKeyAndOrderFront(nil)
        } else {
            // Если окно закрыто, показываем его снова
            showCompletionWindow()
            notificationWindow?.updateMessage(reminderCount: reminderCount)
        }
    }
}
