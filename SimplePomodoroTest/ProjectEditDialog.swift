import Cocoa

class ProjectEditDialog: NSWindowController {
    
    // MARK: - Properties
    
    private var projectManager: ProjectManager
    private var project: Project?
    private var completion: ((Project?) -> Void)?
    
    // UI Elements
    private var nameTextField: NSTextField!
    private var typePopUpButton: NSPopUpButton!
    private var colorWell: NSColorWell!
    private var emojiTextField: NSTextField!
    private var emojiButtons: [NSButton] = []
    private var saveButton: NSButton!
    private var cancelButton: NSButton!
    
    // MARK: - Initialization
    
    init(projectManager: ProjectManager, project: Project? = nil, completion: @escaping (Project?) -> Void) {
        print("🔧 ProjectEditDialog: init вызван")
        self.projectManager = projectManager
        self.project = project
        self.completion = completion

        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 480, height: 420),
            styleMask: [.titled, .closable],
            backing: .buffered,
            defer: false
        )

        super.init(window: window)

        print("🔧 ProjectEditDialog: вызываем setupWindow")
        setupWindow()
        print("🔧 ProjectEditDialog: вызываем setupUI")
        setupUI()
        print("🔧 ProjectEditDialog: вызываем setupActions")
        setupActions()
        print("🔧 ProjectEditDialog: вызываем populateFields")
        populateFields()
        print("🔧 ProjectEditDialog: init завершен")
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Window Setup
    
    private func setupWindow() {
        guard let window = window else { return }
        
        window.title = project == nil ? "Создать проект" : "Редактировать проект"
        window.center()
        window.isReleasedWhenClosed = false
        window.delegate = self
    }
    
    private func setupUI() {
        guard let window = window, let contentView = window.contentView else { return }
        
        // Создаем основной контейнер
        let containerView = NSView()
        containerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(containerView)
        
        // Название проекта
        let nameLabel = NSTextField(labelWithString: "Название:")
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        
        nameTextField = NSTextField()
        nameTextField.translatesAutoresizingMaskIntoConstraints = false
        nameTextField.placeholderString = "Введите название проекта"
        
        // Тип проекта
        let typeLabel = NSTextField(labelWithString: "Тип:")
        typeLabel.translatesAutoresizingMaskIntoConstraints = false
        
        typePopUpButton = NSPopUpButton()
        typePopUpButton.translatesAutoresizingMaskIntoConstraints = false
        
        for projectType in ProjectType.allCases {
            typePopUpButton.addItem(withTitle: "\(projectType.emoji) \(projectType.displayName)")
        }
        
        // Цвет проекта
        let colorLabel = NSTextField(labelWithString: "Цвет:")
        colorLabel.translatesAutoresizingMaskIntoConstraints = false

        colorWell = NSColorWell()
        colorWell.translatesAutoresizingMaskIntoConstraints = false
        colorWell.color = NSColor.systemBlue

        // Кастомный эмодзи
        let emojiLabel = NSTextField(labelWithString: "Кастомная иконка:")
        emojiLabel.translatesAutoresizingMaskIntoConstraints = false

        emojiTextField = NSTextField()
        emojiTextField.translatesAutoresizingMaskIntoConstraints = false
        emojiTextField.placeholderString = "Введите эмодзи или нажмите кнопку ниже"

        // Пояснительный текст
        let helpLabel = NSTextField(labelWithString: "Если не указано, будет использована иконка типа проекта")
        helpLabel.translatesAutoresizingMaskIntoConstraints = false
        helpLabel.font = NSFont.systemFont(ofSize: 11)
        helpLabel.textColor = NSColor.secondaryLabelColor

        // Популярные эмодзи
        let emojiButtonsContainer = NSView()
        emojiButtonsContainer.translatesAutoresizingMaskIntoConstraints = false

        let popularEmojis = ["📝", "💻", "📚", "🎯", "⚡", "🔥", "💡", "🚀"]
        emojiButtons = []

        for emoji in popularEmojis {
            let button = NSButton(title: emoji, target: nil, action: nil)
            button.translatesAutoresizingMaskIntoConstraints = false
            button.isBordered = true
            button.bezelStyle = .rounded
            button.font = NSFont.systemFont(ofSize: 16)
            button.toolTip = "Нажмите чтобы выбрать \(emoji)"
            emojiButtons.append(button)
            emojiButtonsContainer.addSubview(button)
        }
        
        // Кнопки
        let buttonContainer = NSView()
        buttonContainer.translatesAutoresizingMaskIntoConstraints = false
        
        cancelButton = NSButton(title: "Отмена", target: nil, action: nil)
        cancelButton.translatesAutoresizingMaskIntoConstraints = false
        cancelButton.isEnabled = true
        cancelButton.bezelStyle = .rounded

        saveButton = NSButton(title: "Сохранить", target: nil, action: nil)
        saveButton.translatesAutoresizingMaskIntoConstraints = false
        saveButton.keyEquivalent = "\r"
        saveButton.isEnabled = true
        saveButton.bezelStyle = .rounded

        buttonContainer.addSubview(cancelButton)
        buttonContainer.addSubview(saveButton)
        
        // Добавляем элементы
        containerView.addSubview(nameLabel)
        containerView.addSubview(nameTextField)
        containerView.addSubview(typeLabel)
        containerView.addSubview(typePopUpButton)
        containerView.addSubview(colorLabel)
        containerView.addSubview(colorWell)
        containerView.addSubview(emojiLabel)
        containerView.addSubview(emojiTextField)
        containerView.addSubview(helpLabel)
        containerView.addSubview(emojiButtonsContainer)
        containerView.addSubview(buttonContainer)
        
        buttonContainer.addSubview(cancelButton)
        buttonContainer.addSubview(saveButton)
        
        // Constraints
        NSLayoutConstraint.activate([
            // Container
            containerView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            containerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20),
            
            // Name
            nameLabel.topAnchor.constraint(equalTo: containerView.topAnchor),
            nameLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            nameLabel.widthAnchor.constraint(equalToConstant: 80),
            
            nameTextField.topAnchor.constraint(equalTo: containerView.topAnchor),
            nameTextField.leadingAnchor.constraint(equalTo: nameLabel.trailingAnchor, constant: 10),
            nameTextField.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            
            // Type
            typeLabel.topAnchor.constraint(equalTo: nameTextField.bottomAnchor, constant: 20),
            typeLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            typeLabel.widthAnchor.constraint(equalToConstant: 80),
            
            typePopUpButton.topAnchor.constraint(equalTo: nameTextField.bottomAnchor, constant: 20),
            typePopUpButton.leadingAnchor.constraint(equalTo: typeLabel.trailingAnchor, constant: 10),
            typePopUpButton.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            
            // Color
            colorLabel.topAnchor.constraint(equalTo: typePopUpButton.bottomAnchor, constant: 20),
            colorLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            colorLabel.widthAnchor.constraint(equalToConstant: 80),
            
            colorWell.topAnchor.constraint(equalTo: typePopUpButton.bottomAnchor, constant: 20),
            colorWell.leadingAnchor.constraint(equalTo: colorLabel.trailingAnchor, constant: 10),
            colorWell.widthAnchor.constraint(equalToConstant: 50),
            colorWell.heightAnchor.constraint(equalToConstant: 30),
            
            // Emoji
            emojiLabel.topAnchor.constraint(equalTo: colorWell.bottomAnchor, constant: 20),
            emojiLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            emojiLabel.widthAnchor.constraint(equalToConstant: 120),

            emojiTextField.topAnchor.constraint(equalTo: colorWell.bottomAnchor, constant: 20),
            emojiTextField.leadingAnchor.constraint(equalTo: emojiLabel.trailingAnchor, constant: 10),
            emojiTextField.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),

            // Help label
            helpLabel.topAnchor.constraint(equalTo: emojiTextField.bottomAnchor, constant: 5),
            helpLabel.leadingAnchor.constraint(equalTo: emojiLabel.trailingAnchor, constant: 10),
            helpLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),

            // Emoji buttons
            emojiButtonsContainer.topAnchor.constraint(equalTo: helpLabel.bottomAnchor, constant: 10),
            emojiButtonsContainer.leadingAnchor.constraint(equalTo: emojiLabel.trailingAnchor, constant: 10),
            emojiButtonsContainer.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            emojiButtonsContainer.heightAnchor.constraint(equalToConstant: 30),

            // Buttons
            buttonContainer.topAnchor.constraint(equalTo: emojiButtonsContainer.bottomAnchor, constant: 30),
            buttonContainer.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            buttonContainer.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            buttonContainer.bottomAnchor.constraint(equalTo: containerView.bottomAnchor),
            buttonContainer.heightAnchor.constraint(equalToConstant: 40),

            cancelButton.leadingAnchor.constraint(equalTo: buttonContainer.leadingAnchor),
            cancelButton.centerYAnchor.constraint(equalTo: buttonContainer.centerYAnchor),

            saveButton.trailingAnchor.constraint(equalTo: buttonContainer.trailingAnchor),
            saveButton.centerYAnchor.constraint(equalTo: buttonContainer.centerYAnchor)
        ])

        // Constraints для кнопок эмодзи
        for (index, button) in emojiButtons.enumerated() {
            NSLayoutConstraint.activate([
                button.topAnchor.constraint(equalTo: emojiButtonsContainer.topAnchor),
                button.bottomAnchor.constraint(equalTo: emojiButtonsContainer.bottomAnchor),
                button.widthAnchor.constraint(equalToConstant: 30)
            ])

            if index == 0 {
                button.leadingAnchor.constraint(equalTo: emojiButtonsContainer.leadingAnchor).isActive = true
            } else {
                button.leadingAnchor.constraint(equalTo: emojiButtons[index - 1].trailingAnchor, constant: 5).isActive = true
            }
        }
    }

    // MARK: - Actions Setup

    private func setupActions() {
        print("🔧 ProjectEditDialog: setupActions вызван")

        // Настраиваем target/action для кнопок
        cancelButton.target = self
        cancelButton.action = #selector(cancelAction)

        saveButton.target = self
        saveButton.action = #selector(saveAction)

        // Настраиваем target/action для кнопок эмодзи
        for button in emojiButtons {
            button.target = self
            button.action = #selector(emojiButtonClicked(_:))
        }

        print("🔧 ProjectEditDialog: setupActions завершен, настроено \(emojiButtons.count) кнопок эмодзи")
    }

    // MARK: - Data Population

    private func populateFields() {
        guard let project = project else { return }

        nameTextField.stringValue = project.name

        // Устанавливаем тип проекта
        if let typeIndex = ProjectType.allCases.firstIndex(of: project.type) {
            typePopUpButton.selectItem(at: typeIndex)
        }

        // Устанавливаем цвет
        if let colorString = project.color {
            colorWell.color = NSColor(hex: colorString) ?? NSColor.systemBlue
        }

        // Устанавливаем кастомный эмодзи
        emojiTextField.stringValue = project.customEmoji ?? ""
    }
    
    // MARK: - Actions
    
    @objc private func saveAction() {
        print("🔧 ProjectEditDialog: saveAction вызван")
        guard !nameTextField.stringValue.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            showAlert(title: "Ошибка", message: "Введите название проекта")
            return
        }

        let name = nameTextField.stringValue.trimmingCharacters(in: .whitespacesAndNewlines)
        let selectedTypeIndex = typePopUpButton.indexOfSelectedItem
        let projectType = ProjectType.allCases[selectedTypeIndex]
        let color = colorWell.color.hexString
        let customEmoji = emojiTextField.stringValue.trimmingCharacters(in: .whitespacesAndNewlines)
        let finalEmoji = customEmoji.isEmpty ? nil : customEmoji

        if let existingProject = project {
            // Редактируем существующий проект
            var updatedProject = existingProject
            updatedProject.name = name
            updatedProject.type = projectType
            updatedProject.color = color
            updatedProject.customEmoji = finalEmoji

            projectManager.updateProject(updatedProject)
            completion?(updatedProject)
        } else {
            // Создаем новый проект
            let newProject = projectManager.createProject(
                name: name,
                type: projectType,
                color: color,
                customEmoji: finalEmoji
            )
            completion?(newProject)
        }

        close()
    }
    
    @objc private func cancelAction() {
        print("🔧 ProjectEditDialog: cancelAction вызван")
        completion?(nil)
        close()
    }

    @objc private func emojiButtonClicked(_ sender: NSButton) {
        print("🔧 ProjectEditDialog: emojiButtonClicked вызван с \(sender.title)")
        emojiTextField.stringValue = sender.title
    }
    
    private func showAlert(title: String, message: String) {
        let alert = NSAlert()
        alert.messageText = title
        alert.informativeText = message
        alert.addButton(withTitle: "OK")
        alert.runModal()
    }
    
    // MARK: - Public Methods
    
    func showDialog() {
        window?.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)

        // Устанавливаем фокус на поле имени
        window?.makeFirstResponder(nameTextField)

        print("🔧 ProjectEditDialog: showDialog вызван, окно показано")
    }
}

// MARK: - NSWindowDelegate

extension ProjectEditDialog: NSWindowDelegate {
    func windowWillClose(_ notification: Notification) {
        completion?(nil)
    }
}

// MARK: - NSColor Extension

extension NSColor {
    convenience init?(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            return nil
        }
        
        self.init(
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            alpha: Double(a) / 255
        )
    }
    
    var hexString: String {
        guard let rgbColor = usingColorSpace(.deviceRGB) else { return "#000000" }
        let red = Int(round(rgbColor.redComponent * 255))
        let green = Int(round(rgbColor.greenComponent * 255))
        let blue = Int(round(rgbColor.blueComponent * 255))
        return String(format: "#%02X%02X%02X", red, green, blue)
    }
}
