import Foundation

class ProjectManager: ObservableObject {
    
    // MARK: - Properties
    
    @Published private(set) var projects: [Project] = []
    @Published private(set) var favoriteProjectIds: [UUID] = []
    
    private let userDefaults = UserDefaults.standard
    private let projectsKey = "projects"
    private let favoriteProjectsKey = "favoriteProjects"
    
    // MARK: - Initialization
    
    init() {
        loadProjects()
        loadFavoriteProjects()
        
        // Создаем проекты по умолчанию если их нет
        if projects.isEmpty {
            createDefaultProjects()
        }
        
        print("📁 ProjectManager: Инициализирован. Проектов: \(projects.count), Избранных: \(favoriteProjectIds.count)")
    }
    
    // MARK: - CRUD Operations
    
    /// Создает новый проект
    @discardableResult
    func createProject(name: String, type: ProjectType, parentId: UUID? = nil, color: String? = nil, customEmoji: String? = nil) -> Project {
        let project = Project(name: name, type: type, parentId: parentId, color: color, customEmoji: customEmoji)
        projects.append(project)
        saveProjects()

        print("📁 ProjectManager: Создан проект '\(project.name)' типа \(project.type.displayName)")
        return project
    }
    
    /// Обновляет существующий проект
    func updateProject(_ project: Project) {
        if let index = projects.firstIndex(where: { $0.id == project.id }) {
            projects[index] = project
            saveProjects()
            print("📁 ProjectManager: Обновлен проект '\(project.name)'")
        }
    }
    
    /// Удаляет проект
    func deleteProject(_ project: Project) {
        // Удаляем из избранного если есть
        removeFromFavorites(project)
        
        // Удаляем сам проект
        projects.removeAll { $0.id == project.id }
        saveProjects()
        
        print("📁 ProjectManager: Удален проект '\(project.name)'")
    }
    
    /// Получает проект по ID
    func getProject(by id: UUID) -> Project? {
        return projects.first { $0.id == id }
    }
    
    /// Возвращает все активные проекты
    func getActiveProjects() -> [Project] {
        return projects.filter { $0.isActive && !$0.isArchived }
    }
    
    /// Возвращает все архивированные проекты
    func getArchivedProjects() -> [Project] {
        return projects.filter { $0.isArchived }
    }
    
    /// Возвращает проекты по типу
    func getProjects(by type: ProjectType) -> [Project] {
        return projects.filter { $0.type == type && $0.isActive && !$0.isArchived }
    }

    /// Возвращает рабочие проекты
    func getWorkProjects() -> [Project] {
        return projects.filter { $0.isWorkRelated && $0.isActive && !$0.isArchived }
    }

    /// Возвращает личные проекты
    func getPersonalProjects() -> [Project] {
        return projects.filter { !$0.isWorkRelated && $0.isActive && !$0.isArchived }
    }

    /// Возвращает все проекты, сгруппированные по рабочим/личным
    func getProjectsGroupedByWorkType() -> (work: [Project], personal: [Project]) {
        let activeProjects = getActiveProjects()
        let workProjects = activeProjects.filter { $0.isWorkRelated }
        let personalProjects = activeProjects.filter { !$0.isWorkRelated }
        return (work: workProjects, personal: personalProjects)
    }
    
    // MARK: - Favorites Management
    
    /// Возвращает избранные проекты в порядке приоритета
    func getFavoriteProjects() -> [Project] {
        return favoriteProjectIds.compactMap { id in
            projects.first { $0.id == id && $0.isActive && !$0.isArchived }
        }
    }
    
    /// Добавляет проект в избранное
    func addToFavorites(_ project: Project) {
        guard !favoriteProjectIds.contains(project.id) else { return }
        
        // Ограничиваем количество избранных проектов
        if favoriteProjectIds.count >= 5 {
            print("📁 ProjectManager: Достигнуто максимальное количество избранных проектов (5)")
            return
        }
        
        favoriteProjectIds.append(project.id)
        saveFavoriteProjects()
        
        print("📁 ProjectManager: Проект '\(project.name)' добавлен в избранное")
    }
    
    /// Удаляет проект из избранного
    func removeFromFavorites(_ project: Project) {
        favoriteProjectIds.removeAll { $0 == project.id }
        saveFavoriteProjects()
        
        print("📁 ProjectManager: Проект '\(project.name)' удален из избранного")
    }
    
    /// Проверяет, находится ли проект в избранном
    func isFavorite(_ project: Project) -> Bool {
        return favoriteProjectIds.contains(project.id)
    }
    
    /// Изменяет порядок избранных проектов
    func reorderFavorites(from sourceIndex: Int, to destinationIndex: Int) {
        guard sourceIndex < favoriteProjectIds.count && destinationIndex < favoriteProjectIds.count else { return }
        
        let movedProject = favoriteProjectIds.remove(at: sourceIndex)
        favoriteProjectIds.insert(movedProject, at: destinationIndex)
        saveFavoriteProjects()
        
        print("📁 ProjectManager: Изменен порядок избранных проектов")
    }
    
    // MARK: - Usage Tracking
    
    /// Отмечает проект как использованный
    func markProjectAsUsed(_ projectId: UUID) {
        if let index = projects.firstIndex(where: { $0.id == projectId }) {
            projects[index].markAsUsed()
            saveProjects()
        }
    }
    
    // MARK: - Archive Management
    
    /// Архивирует проект
    func archiveProject(_ project: Project) {
        if let index = projects.firstIndex(where: { $0.id == project.id }) {
            projects[index].archive()
            // Удаляем из избранного при архивировании
            removeFromFavorites(project)
            saveProjects()
            
            print("📁 ProjectManager: Проект '\(project.name)' архивирован")
        }
    }
    
    /// Разархивирует проект
    func unarchiveProject(_ project: Project) {
        if let index = projects.firstIndex(where: { $0.id == project.id }) {
            projects[index].unarchive()
            saveProjects()
            
            print("📁 ProjectManager: Проект '\(project.name)' разархивирован")
        }
    }
    
    // MARK: - Private Methods
    
    private func createDefaultProjects() {
        let defaultProjects = Project.createDefaultProjects()
        projects.append(contentsOf: defaultProjects)

        // Добавляем первые 3 проекта в избранное
        favoriteProjectIds = Array(defaultProjects.prefix(3).map { $0.id })

        saveProjects()
        saveFavoriteProjects()

        print("📁 ProjectManager: Созданы проекты по умолчанию")
    }

    // MARK: - Data Persistence

    private func saveProjects() {
        do {
            let data = try JSONEncoder().encode(projects)
            userDefaults.set(data, forKey: projectsKey)
            print("📁 ProjectManager: Проекты сохранены")
        } catch {
            print("❌ ProjectManager: Ошибка сохранения проектов: \(error)")
        }
    }

    private func loadProjects() {
        guard let data = userDefaults.data(forKey: projectsKey) else {
            print("📁 ProjectManager: Данные проектов не найдены")
            return
        }

        do {
            projects = try JSONDecoder().decode([Project].self, from: data)
            print("📁 ProjectManager: Загружено проектов: \(projects.count)")
        } catch {
            print("❌ ProjectManager: Ошибка загрузки проектов: \(error)")
            projects = []
        }
    }

    private func saveFavoriteProjects() {
        do {
            let data = try JSONEncoder().encode(favoriteProjectIds)
            userDefaults.set(data, forKey: favoriteProjectsKey)
            print("📁 ProjectManager: Избранные проекты сохранены")
        } catch {
            print("❌ ProjectManager: Ошибка сохранения избранных проектов: \(error)")
        }
    }

    private func loadFavoriteProjects() {
        guard let data = userDefaults.data(forKey: favoriteProjectsKey) else {
            print("📁 ProjectManager: Данные избранных проектов не найдены")
            return
        }

        do {
            favoriteProjectIds = try JSONDecoder().decode([UUID].self, from: data)
            print("📁 ProjectManager: Загружено избранных проектов: \(favoriteProjectIds.count)")
        } catch {
            print("❌ ProjectManager: Ошибка загрузки избранных проектов: \(error)")
            favoriteProjectIds = []
        }
    }

    // MARK: - Data Management

    /// Очищает все данные проектов (для тестирования)
    func clearAllData() {
        projects.removeAll()
        favoriteProjectIds.removeAll()

        userDefaults.removeObject(forKey: projectsKey)
        userDefaults.removeObject(forKey: favoriteProjectsKey)

        print("📁 ProjectManager: Все данные очищены")
    }
}
