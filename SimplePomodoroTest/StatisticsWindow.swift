import Cocoa

// Кастомный view для hover эффекта в строках таблицы
class HoverTableRowView: NSView {
    private var gradientLayer: CAGradientLayer?

    func setupRow(container: NSView) {
        // Создаем градиентный слой для hover эффекта
        let gradient = CAGradientLayer()
        gradient.colors = [
            NSColor.white.withAlphaComponent(0.08).cgColor,
            NSColor.white.withAlphaComponent(0.04).cgColor
        ]
        gradient.startPoint = CGPoint(x: 0, y: 0)
        gradient.endPoint = CGPoint(x: 1, y: 0)
        gradient.cornerRadius = 8
        gradient.opacity = 0

        container.layer?.insertSublayer(gradient, at: 0)
        self.gradientLayer = gradient

        // Добавляем tracking area для hover эффекта
        let trackingArea = NSTrackingArea(
            rect: container.bounds,
            options: [.mouseEnteredAndExited, .activeInKeyWindow, .inVisibleRect],
            owner: self,
            userInfo: nil
        )
        container.addTrackingArea(trackingArea)

        // Обновляем размер градиента при изменении размера контейнера
        DispatchQueue.main.async { [weak self] in
            self?.gradientLayer?.frame = container.bounds
        }
    }

    override func mouseEntered(with event: NSEvent) {
        super.mouseEntered(with: event)

        // Плавное появление hover эффекта
        CATransaction.begin()
        CATransaction.setAnimationDuration(0.2)
        gradientLayer?.opacity = 1.0
        CATransaction.commit()
    }

    override func mouseExited(with event: NSEvent) {
        super.mouseExited(with: event)

        // Плавное исчезновение hover эффекта
        CATransaction.begin()
        CATransaction.setAnimationDuration(0.2)
        gradientLayer?.opacity = 0.0
        CATransaction.commit()
    }
}

class StatisticsWindow: NSWindowController {
    private var statisticsManager: StatisticsManager
    private var motivationManager: MotivationManager?
    private var analyzer: WorkPatternAnalyzer

    // UI элементы для навигации
    private var periodButtons: [NSButton] = []
    private var periodContainer: NSView!
    private var navigationContainer: NSView!
    private var prevButton: NSButton!
    private var nextButton: NSButton!
    private var periodLabel: NSTextField!

    // UI элементы для статистики
    private var statsContainer: NSView!
    private var analysisContainer: NSView!
    private var scrollView: NSScrollView!
    private var gradientLayer: CAGradientLayer?

    // Навигация по периодам
    private var currentPeriodType: PeriodType = .week
    private var currentOffset: Int = 0 // 0 = текущий период, -1 = предыдущий, +1 = следующий

    enum PeriodType: Int, CaseIterable {
        case day = 0
        case week = 1
        case month = 2
        case year = 3

        var title: String {
            switch self {
            case .day: return "День"
            case .week: return "Неделя"
            case .month: return "Месяц"
            case .year: return "Год"
            }
        }
    }
    
    init(statisticsManager: StatisticsManager, motivationManager: MotivationManager? = nil) {
        self.statisticsManager = statisticsManager
        self.motivationManager = motivationManager
        self.analyzer = WorkPatternAnalyzer(statisticsManager: statisticsManager)

        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 1080, height: 770),
            styleMask: [.titled, .closable, .resizable],
            backing: .buffered,
            defer: false
        )

        super.init(window: window)

        setupWindow()
        setupUI()

        // Обновляем статистику и навигацию после полной инициализации UI
        DispatchQueue.main.async { [weak self] in
            self?.updateStatistics()
            self?.updateNavigationButtons()
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupWindow() {
        guard let window = window else { return }

        window.title = "📊 Статистика"
        window.isReleasedWhenClosed = false
        window.delegate = self
        window.titlebarAppearsTransparent = true
        window.styleMask.insert(.fullSizeContentView)
        // Центрирование перенесено в showWindow()
    }
    
    private func setupUI() {
        guard let window = window else { return }

        let contentView = NSView()
        contentView.wantsLayer = true

        // Создаем красивый glassmorphism градиентный фон
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            NSColor(red: 0.05, green: 0.1, blue: 0.2, alpha: 1.0).cgColor,
            NSColor(red: 0.1, green: 0.15, blue: 0.25, alpha: 1.0).cgColor,
            NSColor(red: 0.05, green: 0.2, blue: 0.15, alpha: 1.0).cgColor,
            NSColor(red: 0.08, green: 0.12, blue: 0.22, alpha: 1.0).cgColor
        ]
        gradientLayer.locations = [0.0, 0.3, 0.7, 1.0]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)

        contentView.layer?.addSublayer(gradientLayer)

        // Сохраняем ссылку на градиент для обновления размера
        self.gradientLayer = gradientLayer

        window.contentView = contentView

        // Обновляем размер градиента после установки contentView
        DispatchQueue.main.async { [weak self] in
            self?.updateGradientFrame()
        }



        // Создаем кастомный контейнер для переключателя периодов в том же стиле, что и навигация
        let periodContainer = NSView()
        periodContainer.wantsLayer = true
        periodContainer.layer?.backgroundColor = NSColor.white.withAlphaComponent(0.1).cgColor
        periodContainer.layer?.cornerRadius = 20
        periodContainer.layer?.borderWidth = 1
        periodContainer.layer?.borderColor = NSColor.white.withAlphaComponent(0.2).cgColor

        // Создаем четыре отдельные кнопки для периодов
        var periodButtons: [NSButton] = []
        let periodTypes = PeriodType.allCases

        for (index, periodType) in periodTypes.enumerated() {
            let button = NSButton(title: periodType.title, target: self, action: #selector(periodButtonClicked(_:)))
            button.tag = index
            button.bezelStyle = .rounded
            button.font = NSFont.systemFont(ofSize: 16, weight: .medium)
            button.contentTintColor = NSColor.white
            button.wantsLayer = true

            // Устанавливаем стиль в зависимости от того, выбрана ли кнопка
            if index == currentPeriodType.rawValue {
                button.layer?.backgroundColor = NSColor.white.withAlphaComponent(0.3).cgColor
            } else {
                button.layer?.backgroundColor = NSColor.clear.cgColor
            }

            button.layer?.cornerRadius = 15
            button.isBordered = false

            periodContainer.addSubview(button)
            button.translatesAutoresizingMaskIntoConstraints = false
            periodButtons.append(button)
        }

        self.periodButtons = periodButtons

        // Размещаем кнопки в контейнере с увеличенными отступами
        let buttonWidth: CGFloat = 70  // Увеличиваем ширину кнопок
        let buttonSpacing: CGFloat = 10  // Увеличиваем промежутки между кнопками
        let containerPadding: CGFloat = 18  // Увеличиваем отступы от краев

        for (index, button) in periodButtons.enumerated() {
            NSLayoutConstraint.activate([
                button.centerYAnchor.constraint(equalTo: periodContainer.centerYAnchor),
                button.widthAnchor.constraint(equalToConstant: buttonWidth),
                button.heightAnchor.constraint(equalToConstant: 32),  // Увеличиваем высоту кнопок
                button.leadingAnchor.constraint(equalTo: periodContainer.leadingAnchor, constant: containerPadding + CGFloat(index) * (buttonWidth + buttonSpacing))
            ])
        }

        self.periodContainer = periodContainer

        // Навигация по периодам
        setupNavigationControls()

        // Скролл-вью для контента
        scrollView = NSScrollView()
        scrollView.hasVerticalScroller = true
        scrollView.hasHorizontalScroller = false
        scrollView.autohidesScrollers = true
        scrollView.backgroundColor = NSColor.clear

        // Убираем автоматические отступы
        scrollView.automaticallyAdjustsContentInsets = false
        scrollView.contentInsets = NSEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)

        let documentView = NSView()
        documentView.translatesAutoresizingMaskIntoConstraints = false
        // Настройка documentView
        documentView.wantsLayer = true
        documentView.layer?.backgroundColor = NSColor.clear.cgColor
        scrollView.documentView = documentView

        // Контейнеры для разных секций
        statsContainer = createTransparentContainer()
        statsContainer.layer?.backgroundColor = NSColor.clear.cgColor

        analysisContainer = createTransparentContainer()
        analysisContainer.layer?.backgroundColor = NSColor.clear.cgColor

        documentView.addSubview(statsContainer)
        documentView.addSubview(analysisContainer)

        // Создаем иконки справа - без контейнеров, только иконки
        let settingsImage = NSImage(systemSymbolName: "gearshape", accessibilityDescription: "Настройки")!
        let settingsLargeImage = settingsImage.withSymbolConfiguration(NSImage.SymbolConfiguration(pointSize: 20, weight: .medium))
        let settingsButton = NSButton(image: settingsLargeImage!, target: nil, action: nil)
        settingsButton.bezelStyle = .shadowlessSquare
        settingsButton.isBordered = false
        settingsButton.contentTintColor = NSColor.white

        let profileImage = NSImage(systemSymbolName: "person.circle", accessibilityDescription: "Профиль")!
        let profileLargeImage = profileImage.withSymbolConfiguration(NSImage.SymbolConfiguration(pointSize: 20, weight: .medium))
        let profileButton = NSButton(image: profileLargeImage!, target: nil, action: nil)
        profileButton.bezelStyle = .shadowlessSquare
        profileButton.isBordered = false
        profileButton.contentTintColor = NSColor.white

        // Layout
        contentView.addSubview(navigationContainer)
        contentView.addSubview(periodContainer)
        contentView.addSubview(settingsButton)
        contentView.addSubview(profileButton)
        contentView.addSubview(scrollView)
        navigationContainer.translatesAutoresizingMaskIntoConstraints = false
        periodContainer.translatesAutoresizingMaskIntoConstraints = false
        settingsButton.translatesAutoresizingMaskIntoConstraints = false
        profileButton.translatesAutoresizingMaskIntoConstraints = false
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        statsContainer.translatesAutoresizingMaskIntoConstraints = false
        analysisContainer.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            // Навигация - размещаем слева
            navigationContainer.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 50),
            navigationContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 50),
            navigationContainer.heightAnchor.constraint(equalToConstant: 40),
            navigationContainer.widthAnchor.constraint(equalToConstant: 420),

            // Селектор периода - размещаем сразу после навигации
            periodContainer.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 50),
            periodContainer.leadingAnchor.constraint(equalTo: navigationContainer.trailingAnchor, constant: 20),
            periodContainer.heightAnchor.constraint(equalToConstant: 40),
            periodContainer.widthAnchor.constraint(equalToConstant: 346), // 4 кнопки по 70px + 3 промежутка по 10px + 2 отступа по 18px = 346px

            // Иконки справа - увеличиваем размер
            profileButton.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 50),
            profileButton.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -50),
            profileButton.heightAnchor.constraint(equalToConstant: 44),
            profileButton.widthAnchor.constraint(equalToConstant: 44),

            settingsButton.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 50),
            settingsButton.trailingAnchor.constraint(equalTo: profileButton.leadingAnchor, constant: -15),
            settingsButton.heightAnchor.constraint(equalToConstant: 44),
            settingsButton.widthAnchor.constraint(equalToConstant: 44),

            // Скролл-вью - начинается после кнопок периодов
            scrollView.topAnchor.constraint(equalTo: periodContainer.bottomAnchor, constant: 30), // расстояяние от выбора периодов до конца градиентной области
            scrollView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),

            // DocumentView constraints - привязываем к scrollView и делаем его высоту равной высоте scrollView
            documentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            documentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            documentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            documentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            documentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            documentView.heightAnchor.constraint(equalTo: scrollView.heightAnchor),

            // Контейнеры в скролл-вью
            statsContainer.topAnchor.constraint(equalTo: documentView.topAnchor, constant: 30), // margin top для картчоек стаистики
            statsContainer.leadingAnchor.constraint(equalTo: documentView.leadingAnchor, constant: 30),
            statsContainer.trailingAnchor.constraint(equalTo: documentView.trailingAnchor, constant: -30),
            statsContainer.widthAnchor.constraint(equalTo: scrollView.widthAnchor, constant: -60),
            statsContainer.heightAnchor.constraint(equalToConstant: 140), // Фиксированная высота

            analysisContainer.topAnchor.constraint(equalTo: statsContainer.bottomAnchor, constant: 15),
            analysisContainer.leadingAnchor.constraint(equalTo: documentView.leadingAnchor, constant: 30),
            analysisContainer.trailingAnchor.constraint(equalTo: documentView.trailingAnchor, constant: -30),
            analysisContainer.heightAnchor.constraint(equalToConstant: 350),

            // Высота documentView определяется содержимым
            documentView.bottomAnchor.constraint(equalTo: analysisContainer.bottomAnchor, constant: 15)
        ])
    }

    private func setupNavigationControls() {
        navigationContainer = NSView()
        navigationContainer.wantsLayer = true
        navigationContainer.layer?.backgroundColor = NSColor.white.withAlphaComponent(0.1).cgColor
        navigationContainer.layer?.cornerRadius = 20
        navigationContainer.layer?.borderWidth = 1
        navigationContainer.layer?.borderColor = NSColor.white.withAlphaComponent(0.2).cgColor

        prevButton = NSButton(title: "◀", target: self, action: #selector(previousPeriod))
        prevButton.bezelStyle = .circular
        prevButton.font = NSFont.systemFont(ofSize: 16, weight: .medium)
        prevButton.contentTintColor = NSColor.white
        prevButton.wantsLayer = true
        prevButton.layer?.backgroundColor = NSColor.white.withAlphaComponent(0.1).cgColor
        prevButton.layer?.cornerRadius = 15

        nextButton = NSButton(title: "▶", target: self, action: #selector(nextPeriod))
        nextButton.bezelStyle = .circular
        nextButton.font = NSFont.systemFont(ofSize: 16, weight: .medium)
        nextButton.contentTintColor = NSColor.white
        nextButton.wantsLayer = true
        nextButton.layer?.backgroundColor = NSColor.white.withAlphaComponent(0.1).cgColor
        nextButton.layer?.cornerRadius = 15

        periodLabel = NSTextField(labelWithString: getCurrentPeriodText())
        periodLabel.font = NSFont.systemFont(ofSize: 16, weight: .semibold)
        periodLabel.textColor = NSColor.white
        periodLabel.alignment = .center
        periodLabel.isBezeled = false
        periodLabel.isEditable = false
        periodLabel.backgroundColor = NSColor.clear

        navigationContainer.addSubview(prevButton)
        navigationContainer.addSubview(periodLabel)
        navigationContainer.addSubview(nextButton)

        prevButton.translatesAutoresizingMaskIntoConstraints = false
        periodLabel.translatesAutoresizingMaskIntoConstraints = false
        nextButton.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            prevButton.leadingAnchor.constraint(equalTo: navigationContainer.leadingAnchor, constant: 5),
            prevButton.centerYAnchor.constraint(equalTo: navigationContainer.centerYAnchor),
            prevButton.widthAnchor.constraint(equalToConstant: 30),
            prevButton.heightAnchor.constraint(equalToConstant: 30),

            periodLabel.centerXAnchor.constraint(equalTo: navigationContainer.centerXAnchor),
            periodLabel.centerYAnchor.constraint(equalTo: navigationContainer.centerYAnchor),
            periodLabel.leadingAnchor.constraint(equalTo: prevButton.trailingAnchor, constant: 15),
            periodLabel.trailingAnchor.constraint(equalTo: nextButton.leadingAnchor, constant: -15),

            nextButton.trailingAnchor.constraint(equalTo: navigationContainer.trailingAnchor, constant: -5),
            nextButton.centerYAnchor.constraint(equalTo: navigationContainer.centerYAnchor),
            nextButton.widthAnchor.constraint(equalToConstant: 30),
            nextButton.heightAnchor.constraint(equalToConstant: 30)
        ])
    }

    private func createModernContainer() -> NSView {
        let container = NSView()
        container.wantsLayer = true

        // Улучшенный glassmorphism эффект
        container.layer?.backgroundColor = NSColor.white.withAlphaComponent(0.12).cgColor
        container.layer?.cornerRadius = 24
        container.layer?.borderWidth = 1.5
        container.layer?.borderColor = NSColor.white.withAlphaComponent(0.25).cgColor

        // Улучшенная тень
        container.layer?.shadowColor = NSColor.black.cgColor
        container.layer?.shadowOpacity = 0.25
        container.layer?.shadowOffset = CGSize(width: 0, height: 8)
        container.layer?.shadowRadius = 16

        return container
    }



    
    private func updateStatistics() {
        // Проверяем, что UI элементы инициализированы
        guard statsContainer != nil, analysisContainer != nil else { return }

        // Получаем данные для текущего периода
        let analysisPeriod = getCurrentAnalysisPeriod()
        let intervals = analyzer.getIntervalsForPeriod(analysisPeriod)
        let pattern = analyzer.analyzeWorkPattern(for: analysisPeriod)

        // Обновляем статистику
        updateStatsSection(intervals: intervals, pattern: pattern)

        // Обновляем анализ и рекомендации
        updateAnalysisSection(pattern: pattern)

        // Настраиваем двухколоночный layout для недельного периода
        if currentPeriodType == .week {
            setupTwoColumnLayout(intervals: intervals, pattern: pattern)
        }

        // Скроллим в начало при обновлении данных
        DispatchQueue.main.async { [weak self] in
            self?.scrollView.documentView?.scroll(NSPoint.zero)
        }
    }

    private func getCurrentAnalysisPeriod() -> AnalysisPeriod {
        let calendar = Calendar.current
        let now = Date()
        let startOfToday = calendar.startOfDay(for: now)

        switch currentPeriodType {
        case .day:
            guard let targetDate = calendar.date(byAdding: .day, value: currentOffset, to: startOfToday) else {
                let endOfToday = calendar.date(byAdding: .day, value: 1, to: startOfToday) ?? startOfToday
                return .customRange(start: startOfToday, end: endOfToday)
            }

            let startOfTargetDay = calendar.startOfDay(for: targetDate)
            guard let endOfTargetDay = calendar.date(byAdding: .day, value: 1, to: startOfTargetDay) else {
                return .customRange(start: startOfTargetDay, end: startOfTargetDay)
            }

            return .customRange(start: startOfTargetDay, end: endOfTargetDay)

        case .week:
            let calendar = Calendar.current

            // Используем тот же алгоритм, что и в DemoDataManager для синхронизации
            let weekday = calendar.component(.weekday, from: now)
            let daysFromMonday = (weekday == 1) ? 6 : weekday - 2
            let startOfCurrentWeek = calendar.date(byAdding: .day, value: -daysFromMonday, to: calendar.startOfDay(for: now))!

            // Смещаем на нужное количество недель
            guard let targetWeekStart = calendar.date(byAdding: .day, value: currentOffset * 7, to: startOfCurrentWeek),
                  let targetWeekEnd = calendar.date(byAdding: .day, value: 7, to: targetWeekStart) else {
                return .lastWeek
            }

            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "dd.MM.yyyy HH:mm"
            print("📅 StatisticsWindow: период для offset \(currentOffset): \(dateFormatter.string(from: targetWeekStart)) - \(dateFormatter.string(from: targetWeekEnd))")

            return .customRange(start: targetWeekStart, end: targetWeekEnd)

        case .month:
            guard let targetDate = calendar.date(byAdding: .month, value: currentOffset, to: now),
                  let monthInterval = calendar.dateInterval(of: .month, for: targetDate) else {
                return .lastMonth
            }
            return .customRange(start: monthInterval.start, end: monthInterval.end)

        case .year:
            guard let targetDate = calendar.date(byAdding: .year, value: currentOffset, to: now),
                  let yearInterval = calendar.dateInterval(of: .year, for: targetDate) else {
                return .lastThreeMonths
            }
            return .customRange(start: yearInterval.start, end: yearInterval.end)
        }
    }

    private func updateStatsSection(intervals: [(Date, TimeInterval)], pattern: WorkPattern) {
        // Проверяем, что statsContainer инициализирован
        guard let statsContainer = statsContainer else { return }

        // Очищаем предыдущий контент
        clearContainer(statsContainer)

        // Подсчитываем статистику
        let totalIntervals = intervals.count
        let totalHours = Double(totalIntervals) * 25.0 / 60.0
        let workingDays = Set(intervals.map { Calendar.current.startOfDay(for: $0.0) }).count

        // Создаем статистику в зависимости от типа периода
        var stats: [(String, String)] = []

        if currentPeriodType == .day {
            // Для дня показываем упрощенную статистику
            stats = [
                ("Интервалов", "\(totalIntervals)"),
                ("Время работы", "\(String(format: "%.1f", totalHours)) ч"),
                ("Средняя длительность интервала", formatDuration(pattern.averageIntervalDuration)),
                ("Время начала", formatStartTime(pattern.averageStartTime))
            ]
        } else {
            // Для больших периодов показываем полную статистику
            stats = [
                ("Всего интервалов", "\(totalIntervals)"),
                ("Рабочих дней", "\(workingDays)"),
                ("Среднее интервалов в день", String(format: "%.1f", pattern.averageIntervalsPerDay)),
                ("Средняя длительность интервала", formatDuration(pattern.averageIntervalDuration)),
                ("Стабильность", String(format: "%.0f%%", pattern.consistencyScore * 100)),
                ("Среднее время начала", formatStartTime(pattern.averageStartTime))
            ]
        }

        // Добавляем колонки статистики с равномерным распределением
        let totalSpacing = CGFloat(40) // Отступы по краям
        let interItemSpacing = CGFloat(20) // Отступы между элементами
        let availableWidth = 1200 - totalSpacing - (interItemSpacing * CGFloat(stats.count - 1))
        let columnWidth = availableWidth / CGFloat(stats.count)

        var constraints: [NSLayoutConstraint] = []

        for (index, (label, value)) in stats.enumerated() {
            let column = createHorizontalStatColumn(label: label, value: value)
            statsContainer.addSubview(column)
            column.translatesAutoresizingMaskIntoConstraints = false

            // Вертикальные constraints
            constraints.append(column.topAnchor.constraint(equalTo: statsContainer.topAnchor, constant: 5))
            constraints.append(column.bottomAnchor.constraint(equalTo: statsContainer.bottomAnchor, constant: -15))
            constraints.append(column.heightAnchor.constraint(equalToConstant: 120))

            // Горизонтальные constraints с равномерным распределением
            if index == 0 {
                // Первый элемент
                constraints.append(column.leadingAnchor.constraint(equalTo: statsContainer.leadingAnchor, constant: 20))
            } else {
                // Остальные элементы - отступ от предыдущего
                let previousColumn = statsContainer.subviews[index - 1]
                constraints.append(column.leadingAnchor.constraint(equalTo: previousColumn.trailingAnchor, constant: interItemSpacing))
            }

            if index == stats.count - 1 {
                // Последний элемент
                constraints.append(column.trailingAnchor.constraint(equalTo: statsContainer.trailingAnchor, constant: -20))
            }

            // Ширина колонки
            constraints.append(column.widthAnchor.constraint(equalToConstant: columnWidth))
        }

        NSLayoutConstraint.activate(constraints)
    }

    private func updateAnalysisSection(pattern: WorkPattern) {
        // Проверяем, что analysisContainer инициализирован
        guard let analysisContainer = analysisContainer else { return }

        // Очищаем предыдущий контент
        clearContainer(analysisContainer)

        // Добавляем заголовок
        let titleLabel = NSTextField(labelWithString: "🔍 Анализ и рекомендации")
        titleLabel.font = NSFont.systemFont(ofSize: 18, weight: .bold)
        titleLabel.textColor = NSColor.white
        titleLabel.isBezeled = false
        titleLabel.isEditable = false
        titleLabel.backgroundColor = NSColor.clear

        analysisContainer.addSubview(titleLabel)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: analysisContainer.topAnchor, constant: 20),
            titleLabel.leadingAnchor.constraint(equalTo: analysisContainer.leadingAnchor, constant: 30),
            titleLabel.trailingAnchor.constraint(lessThanOrEqualTo: analysisContainer.trailingAnchor, constant: -30)
        ])

        var lastView: NSView = titleLabel

        // Создаем таблицу рисков и рекомендаций
        let maxCount = max(pattern.riskFactors.count, pattern.recommendations.count)

        if maxCount > 0 {
            for i in 0..<maxCount {
                let rowView = createAnalysisRow(
                    risk: i < pattern.riskFactors.count ? pattern.riskFactors[i] : nil,
                    recommendation: i < pattern.recommendations.count ? pattern.recommendations[i] : nil
                )

                analysisContainer.addSubview(rowView)
                rowView.translatesAutoresizingMaskIntoConstraints = false

                NSLayoutConstraint.activate([
                    rowView.topAnchor.constraint(equalTo: lastView.bottomAnchor, constant: i == 0 ? 15 : 4),
                    rowView.leadingAnchor.constraint(equalTo: analysisContainer.leadingAnchor, constant: 30),
                    rowView.trailingAnchor.constraint(equalTo: analysisContainer.trailingAnchor, constant: -30)
                ])

                lastView = rowView
            }
        } else {
            // Если нет рисков и рекомендаций - простое сообщение
            let noDataLabel = NSTextField(labelWithString: "✨ Отличная работа! Никаких рисков не выявлено.")
            noDataLabel.font = NSFont.systemFont(ofSize: 16, weight: .medium)
            noDataLabel.textColor = NSColor.white.withAlphaComponent(0.7)
            noDataLabel.alignment = .center
            noDataLabel.isBezeled = false
            noDataLabel.isEditable = false
            noDataLabel.backgroundColor = NSColor.clear

            analysisContainer.addSubview(noDataLabel)
            noDataLabel.translatesAutoresizingMaskIntoConstraints = false

            NSLayoutConstraint.activate([
                noDataLabel.topAnchor.constraint(equalTo: lastView.bottomAnchor, constant: 30),
                noDataLabel.leadingAnchor.constraint(equalTo: analysisContainer.leadingAnchor, constant: 60),
                noDataLabel.trailingAnchor.constraint(equalTo: analysisContainer.trailingAnchor, constant: -60)
            ])

            lastView = noDataLabel
        }

        // Устанавливаем высоту контейнера
        NSLayoutConstraint.activate([
            analysisContainer.bottomAnchor.constraint(equalTo: lastView.bottomAnchor, constant: 20)
        ])
    }

    @objc func refreshStatistics() {
        updateStatistics()
    }

    func showWindow() {
        updateStatistics()
        window?.makeKeyAndOrderFront(nil)

        // Центрируем окно явно вычисляя позицию и обновляем градиент
        DispatchQueue.main.async { [weak self] in
            self?.centerWindowExplicitly()
            self?.updateGradientFrame()
        }

        NSApp.activate(ignoringOtherApps: true)
    }



    private func centerWindowExplicitly() {
        guard let window = window,
              let screen = NSScreen.main else { return }

        let screenFrame = screen.visibleFrame
        let windowFrame = window.frame

        let x = screenFrame.origin.x + (screenFrame.width - windowFrame.width) / 2
        let y = screenFrame.origin.y + (screenFrame.height - windowFrame.height) / 2

        window.setFrameOrigin(NSPoint(x: x, y: y))
    }

    // MARK: - Вспомогательные функции

    private func updateGradientFrame() {
        guard let gradientLayer = gradientLayer,
              let contentView = window?.contentView else {
            return
        }

        let bounds = contentView.bounds

        DispatchQueue.main.async {
            gradientLayer.frame = bounds
        }
    }

    private func clearContainer(_ container: NSView?) {
        // Удаляем все subviews
        guard let container = container else { return }
        container.subviews.forEach { $0.removeFromSuperview() }
    }

    private func createModernStatCard(label: String, value: String) -> NSView {
        let container = NSView()
        container.wantsLayer = true
        container.layer?.backgroundColor = NSColor.white.withAlphaComponent(0.05).cgColor
        container.layer?.cornerRadius = 12
        container.layer?.borderWidth = 1
        container.layer?.borderColor = NSColor.white.withAlphaComponent(0.1).cgColor

        let labelField = NSTextField(labelWithString: label)
        labelField.font = NSFont.systemFont(ofSize: 14, weight: .medium)
        labelField.textColor = NSColor.white.withAlphaComponent(0.8)
        labelField.isBezeled = false
        labelField.isEditable = false
        labelField.backgroundColor = NSColor.clear

        let valueField = NSTextField(labelWithString: value)
        valueField.font = NSFont.systemFont(ofSize: 18, weight: .bold)
        valueField.textColor = NSColor.white
        valueField.isBezeled = false
        valueField.isEditable = false
        valueField.backgroundColor = NSColor.clear

        container.addSubview(labelField)
        container.addSubview(valueField)

        labelField.translatesAutoresizingMaskIntoConstraints = false
        valueField.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            labelField.topAnchor.constraint(equalTo: container.topAnchor, constant: 12),
            labelField.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 16),
            labelField.trailingAnchor.constraint(lessThanOrEqualTo: container.trailingAnchor, constant: -16),

            valueField.topAnchor.constraint(equalTo: labelField.bottomAnchor, constant: 4),
            valueField.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 16),
            valueField.trailingAnchor.constraint(lessThanOrEqualTo: container.trailingAnchor, constant: -16),
            valueField.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -12),

            container.heightAnchor.constraint(equalToConstant: 70)
        ])

        return container
    }

    private func createTransparentContainer() -> NSView {
        let container = NSView()
        container.wantsLayer = true
        // Временная отладка - делаем контейнер видимым
        container.layer?.backgroundColor = NSColor.green.withAlphaComponent(0.3).cgColor
        return container
    }

    private func createHorizontalStatColumn(label: String, value: String) -> NSView {
        let container = NSView()
        container.wantsLayer = true

        // Создаем темный базовый фон
        container.layer?.backgroundColor = NSColor(red: 0.12, green: 0.12, blue: 0.15, alpha: 1.0).cgColor
        container.layer?.cornerRadius = 12

        // Создаем первый радиальный градиент (сверху справа)
        let gradientLayer1 = CAGradientLayer()
        gradientLayer1.type = .radial

        // Получаем цвета для радиального градиента
        let gradientColors = getRadialGradientColors(for: label)
        gradientLayer1.colors = gradientColors.primary

        // Настраиваем позицию - центр на краю правого верхнего угла
        gradientLayer1.startPoint = CGPoint(x: 1.0, y: 0.0)  // Точно на углу
        gradientLayer1.endPoint = CGPoint(x: 0.0, y: 1.0)    // Радиус до противоположного угла
        gradientLayer1.cornerRadius = 12

        // Создаем второй радиальный градиент (снизу слева)
        let gradientLayer2 = CAGradientLayer()
        gradientLayer2.type = .radial

        gradientLayer2.colors = gradientColors.secondary

        // Настраиваем позицию - центр на краю левого нижнего угла
        gradientLayer2.startPoint = CGPoint(x: 0.0, y: 1.0)  // Точно на углу
        gradientLayer2.endPoint = CGPoint(x: 1.0, y: 0.0)    // Радиус до противоположного угла
        gradientLayer2.cornerRadius = 12

        container.layer?.addSublayer(gradientLayer1)
        container.layer?.addSublayer(gradientLayer2)

        // Добавляем полупрозрачный темно-серый overlay поверх градиентов
        let overlayLayer = CALayer()
        overlayLayer.backgroundColor = NSColor(red: 0.15, green: 0.15, blue: 0.18, alpha: 0.6).cgColor
        overlayLayer.cornerRadius = 12
        container.layer?.addSublayer(overlayLayer)

        // Добавляем тонкую границу поверх градиента
        container.layer?.borderWidth = 1
        container.layer?.borderColor = NSColor(red: 0.25, green: 0.25, blue: 0.28, alpha: 0.3).cgColor

        // Красивая большая цифра вместо иконки
        let displayValue = getNumberForLabel(label, value: value)

        // Проверяем, есть ли единицы измерения в значении
        let numberLabel: NSTextField
        if value.contains("мин") || value.contains("ч") {
            // Создаем составной лейбл для значений с единицами измерения
            numberLabel = createCompositeNumberLabel(value: value)
        } else {
            // Обычный лейбл для остальных значений
            numberLabel = NSTextField(labelWithString: displayValue)
            numberLabel.font = NSFont.monospacedDigitSystemFont(ofSize: 36, weight: .ultraLight)
            numberLabel.textColor = NSColor.white.withAlphaComponent(0.95)
            numberLabel.isBezeled = false
            numberLabel.isEditable = false
            numberLabel.backgroundColor = NSColor.clear
            numberLabel.alignment = NSTextAlignment.left
            numberLabel.drawsBackground = false
        }

        // Индикатор из четырех точек в правом верхнем углу
        let dotsIndicator = createDotsIndicator(for: label, value: value)

        // Описательный текст под иконкой
        let labelField = NSTextField(labelWithString: label)
        labelField.font = NSFont.systemFont(ofSize: 11, weight: .light)  // Делаем шрифт тоньше
        labelField.textColor = NSColor.white.withAlphaComponent(0.8)
        labelField.isBezeled = false
        labelField.isEditable = false
        labelField.backgroundColor = NSColor.clear
        labelField.alignment = .left
        labelField.lineBreakMode = .byWordWrapping
        labelField.maximumNumberOfLines = 2  // Разрешаем 2 строки
        labelField.drawsBackground = false



        container.addSubview(numberLabel)
        container.addSubview(dotsIndicator)
        container.addSubview(labelField)

        numberLabel.translatesAutoresizingMaskIntoConstraints = false
        dotsIndicator.translatesAutoresizingMaskIntoConstraints = false
        labelField.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            // Красивая большая цифра в левом верхнем углу
            numberLabel.topAnchor.constraint(equalTo: container.topAnchor, constant: 16),
            numberLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 16),
            numberLabel.widthAnchor.constraint(greaterThanOrEqualToConstant: 60),
            numberLabel.heightAnchor.constraint(equalToConstant: 50),

            // Индикатор из четырех точек в правом верхнем углу
            dotsIndicator.topAnchor.constraint(equalTo: container.topAnchor, constant: 16),
            dotsIndicator.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -16),

            // Описательный текст под цифрой с равномерными отступами
            labelField.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 16),
            labelField.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -16),
            labelField.topAnchor.constraint(equalTo: numberLabel.bottomAnchor, constant: 0),  // Такой же отступ как сверху
            labelField.heightAnchor.constraint(greaterThanOrEqualToConstant: 30)  // Минимальная высота для 2 строк
        ])

        // Обновляем размер градиентов и overlay при изменении размера контейнера
        DispatchQueue.main.async {
            gradientLayer1.frame = container.bounds
            gradientLayer2.frame = container.bounds
            overlayLayer.frame = container.bounds
        }

        return container
    }

    // Функция для создания составного лейбла (число + единица измерения)
    private func createCompositeNumberLabel(value: String) -> NSTextField {
        // Извлекаем число и единицу измерения
        let numberPattern = #"(\d+(?:\.\d+)?)\s*(мин|ч)"#
        let regex = try! NSRegularExpression(pattern: numberPattern)
        let range = NSRange(value.startIndex..<value.endIndex, in: value)

        if let match = regex.firstMatch(in: value, range: range) {
            let numberRange = Range(match.range(at: 1), in: value)!
            let unitRange = Range(match.range(at: 2), in: value)!

            let numberString = String(value[numberRange])
            let unitString = String(value[unitRange])

            // Создаем attributed string с разными размерами шрифтов
            let attributedString = NSMutableAttributedString()

            // Большое число
            let numberAttributes: [NSAttributedString.Key: Any] = [
                .font: NSFont.monospacedDigitSystemFont(ofSize: 36, weight: .ultraLight),
                .foregroundColor: NSColor.white.withAlphaComponent(0.95)
            ]
            attributedString.append(NSAttributedString(string: numberString, attributes: numberAttributes))

            // Маленькая единица измерения (выравнивается по нижнему краю цифры)
            let unitAttributes: [NSAttributedString.Key: Any] = [
                .font: NSFont.systemFont(ofSize: 14, weight: .light),
                .foregroundColor: NSColor.white.withAlphaComponent(0.7)
            ]
            attributedString.append(NSAttributedString(string: " " + unitString, attributes: unitAttributes))

            let numberLabel = NSTextField()
            numberLabel.attributedStringValue = attributedString
            numberLabel.isBezeled = false
            numberLabel.isEditable = false
            numberLabel.backgroundColor = NSColor.clear
            numberLabel.alignment = NSTextAlignment.left
            numberLabel.drawsBackground = false

            return numberLabel
        }

        // Fallback - обычный лейбл
        let numberLabel = NSTextField(labelWithString: value)
        numberLabel.font = NSFont.monospacedDigitSystemFont(ofSize: 36, weight: .ultraLight)
        numberLabel.textColor = NSColor.white.withAlphaComponent(0.95)
        numberLabel.isBezeled = false
        numberLabel.isEditable = false
        numberLabel.backgroundColor = NSColor.clear
        numberLabel.alignment = NSTextAlignment.left
        numberLabel.drawsBackground = false

        return numberLabel
    }

    // Функция для получения красивой цифры из значения
    private func getNumberForLabel(_ label: String, value: String) -> String {
        // Специальная обработка для времени (формат HH:MM)
        if label.contains("время") && value.contains(":") {
            return value // Возвращаем время как есть
        }

        // Специальная обработка для процентов
        if value.contains("%") {
            return value // Возвращаем проценты как есть
        }

        // Специальная обработка для значений с единицами измерения (ч, мин)
        if value.contains("ч") || value.contains("мин") {
            return value // Возвращаем с единицами как есть
        }

        // Для остальных случаев пытаемся извлечь число (включая десятичные)
        // Сначала пробуем найти число с точкой
        let numberPattern = #"[\d]+\.?[\d]*"#
        if let range = value.range(of: numberPattern, options: .regularExpression) {
            let numberString = String(value[range])
            if let doubleValue = Double(numberString) {
                // Если число целое, показываем без десятичной части
                if doubleValue == floor(doubleValue) {
                    return "\(Int(doubleValue))"
                } else {
                    // Показываем с одним знаком после запятой
                    return String(format: "%.1f", doubleValue)
                }
            }
        }

        // Если не удалось извлечь число, возвращаем первые символы
        if value.count >= 2 {
            return String(value.prefix(2))
        }
        return value
    }

    // Функция для создания индикатора из четырех точек
    private func createDotsIndicator(for label: String, value: String) -> NSView {
        let container = NSView()
        container.wantsLayer = true

        // Получаем уровень (пока рандомно для тестирования)
        let level = getIndicatorLevel(for: label, value: value)

        // Создаем четыре точки (уменьшенные на 20%)
        let dotSize: CGFloat = 5  // было 6, стало 5 (уменьшение на ~17%)
        let spacing: CGFloat = 3  // было 4, стало 3 (уменьшение на 25%)

        for i in 0..<4 {
            let dot = NSView()
            dot.wantsLayer = true
            dot.layer?.cornerRadius = dotSize / 2

            // Определяем, активна ли точка (заполняем снизу вверх)
            // i=0 - нижняя точка, i=3 - верхняя точка
            let dotIndex = 3 - i  // инвертируем индекс для заполнения снизу

            if dotIndex < level {
                // Активная точка - цветные в зависимости от уровня
                switch level {
                case 1:
                    dot.layer?.backgroundColor = NSColor.systemRed.cgColor      // 1 точка - красный
                case 2:
                    dot.layer?.backgroundColor = NSColor.systemOrange.cgColor   // 2 точки - оранжевый
                case 3:
                    dot.layer?.backgroundColor = NSColor.systemYellow.cgColor   // 3 точки - желтый
                case 4:
                    dot.layer?.backgroundColor = NSColor.systemGreen.cgColor    // 4 точки - зеленый
                default:
                    dot.layer?.backgroundColor = NSColor.systemGray.cgColor
                }
            } else {
                // Неактивная точка - темно-серая
                dot.layer?.backgroundColor = NSColor.darkGray.withAlphaComponent(0.3).cgColor
            }

            container.addSubview(dot)
            dot.translatesAutoresizingMaskIntoConstraints = false

            NSLayoutConstraint.activate([
                dot.widthAnchor.constraint(equalToConstant: dotSize),
                dot.heightAnchor.constraint(equalToConstant: dotSize),
                dot.centerXAnchor.constraint(equalTo: container.centerXAnchor),
                dot.topAnchor.constraint(equalTo: container.topAnchor, constant: CGFloat(i) * (dotSize + spacing))
            ])
        }

        // Размер контейнера
        let totalHeight = 4 * dotSize + 3 * spacing
        NSLayoutConstraint.activate([
            container.widthAnchor.constraint(equalToConstant: dotSize),
            container.heightAnchor.constraint(equalToConstant: totalHeight)
        ])

        return container
    }

    // Функция для получения уровня индикатора на основе значений
    private func getIndicatorLevel(for label: String, value: String) -> Int {
        // Извлекаем числовое значение
        let numberPattern = #"[\d]+\.?[\d]*"#
        if let range = value.range(of: numberPattern, options: .regularExpression) {
            let numberString = String(value[range])
            if let doubleValue = Double(numberString) {

                // Логика для разных типов показателей
                if label.contains("интервалов") && label.contains("день") {
                    // Среднее интервалов в день: 0-1=плохо, 1-3=средне, 3-5=хорошо, 5+=отлично
                    if doubleValue < 1.0 { return 1 }
                    else if doubleValue < 3.0 { return 2 }
                    else if doubleValue < 5.0 { return 3 }
                    else { return 4 }
                }
                else if label.contains("Стабильность") {
                    // Стабильность в процентах: 0-25%=плохо, 25-50%=средне, 50-75%=хорошо, 75%+=отлично
                    if doubleValue < 25 { return 1 }
                    else if doubleValue < 50 { return 2 }
                    else if doubleValue < 75 { return 3 }
                    else { return 4 }
                }
                else if label.contains("интервалов") {
                    // Общее количество интервалов: 0-5=плохо, 5-15=средне, 15-25=хорошо, 25+=отлично
                    if doubleValue < 5 { return 1 }
                    else if doubleValue < 15 { return 2 }
                    else if doubleValue < 25 { return 3 }
                    else { return 4 }
                }
                else {
                    // Для остальных показателей - средний уровень
                    return 2
                }
            }
        }

        // По умолчанию средний уровень
        return 2
    }

    // Функция для получения описания вместо значения
    private func getDescriptionForLabel(_ label: String) -> String {
        switch label {
        case "Всего интервалов", "Интервалов":
            return "интервалов завершено"
        case "Рабочих дней":
            return "дней активности"
        case "Среднее в день":
            return "в среднем за день"
        case "Средняя длительность":
            return "средняя длительность"
        case "Стабильность":
            return "стабильность работы"
        case "Среднее время начала", "Время начала":
            return "время начала работы"
        case "Время работы":
            return "общее время работы"
        default:
            return "показатель"
        }
    }

    // Функция для получения радиальных градиентов с разными цветами
    private func getRadialGradientColors(for label: String) -> (primary: [CGColor], secondary: [CGColor]) {
        switch label {
        case "Всего интервалов", "Интервалов":
            // Синий + Оранжевый (контрастная пара) - более прозрачные
            let primary = [
                NSColor(red: 0.2, green: 0.4, blue: 0.9, alpha: 0.4).cgColor,  // Яркий синий
                NSColor(red: 0.1, green: 0.2, blue: 0.5, alpha: 0.2).cgColor,
                NSColor(red: 0.05, green: 0.1, blue: 0.25, alpha: 0.0).cgColor
            ]
            let secondary = [
                NSColor(red: 1.0, green: 0.5, blue: 0.2, alpha: 0.35).cgColor,  // Оранжевый
                NSColor(red: 0.5, green: 0.25, blue: 0.1, alpha: 0.18).cgColor,
                NSColor(red: 0.25, green: 0.12, blue: 0.05, alpha: 0.0).cgColor
            ]
            return (primary: primary, secondary: secondary)

        case "Рабочих дней":
            // Зеленый + Красный (контрастная пара) - более прозрачные
            let primary = [
                NSColor(red: 0.2, green: 0.8, blue: 0.4, alpha: 0.4).cgColor,  // Яркий зеленый
                NSColor(red: 0.1, green: 0.4, blue: 0.2, alpha: 0.2).cgColor,
                NSColor(red: 0.05, green: 0.2, blue: 0.1, alpha: 0.0).cgColor
            ]
            let secondary = [
                NSColor(red: 0.9, green: 0.3, blue: 0.3, alpha: 0.35).cgColor,  // Красный
                NSColor(red: 0.45, green: 0.15, blue: 0.15, alpha: 0.18).cgColor,
                NSColor(red: 0.22, green: 0.08, blue: 0.08, alpha: 0.0).cgColor
            ]
            return (primary: primary, secondary: secondary)

        case "Среднее в день":
            // Фиолетовый + Желтый (контрастная пара) - более прозрачные
            let primary = [
                NSColor(red: 0.7, green: 0.3, blue: 0.9, alpha: 0.4).cgColor,  // Яркий фиолетовый
                NSColor(red: 0.35, green: 0.15, blue: 0.45, alpha: 0.2).cgColor,
                NSColor(red: 0.18, green: 0.08, blue: 0.22, alpha: 0.0).cgColor
            ]
            let secondary = [
                NSColor(red: 1.0, green: 0.9, blue: 0.2, alpha: 0.35).cgColor,  // Желтый
                NSColor(red: 0.5, green: 0.45, blue: 0.1, alpha: 0.18).cgColor,
                NSColor(red: 0.25, green: 0.22, blue: 0.05, alpha: 0.0).cgColor
            ]
            return (primary: primary, secondary: secondary)

        case "Средняя длительность":
            // Оранжевый + Синий (контрастная пара) - более прозрачные
            let primary = [
                NSColor(red: 1.0, green: 0.5, blue: 0.2, alpha: 0.4).cgColor,  // Яркий оранжевый
                NSColor(red: 0.5, green: 0.25, blue: 0.1, alpha: 0.2).cgColor,
                NSColor(red: 0.25, green: 0.12, blue: 0.05, alpha: 0.0).cgColor
            ]
            let secondary = [
                NSColor(red: 0.2, green: 0.4, blue: 0.9, alpha: 0.35).cgColor,  // Синий
                NSColor(red: 0.1, green: 0.2, blue: 0.45, alpha: 0.18).cgColor,
                NSColor(red: 0.05, green: 0.1, blue: 0.22, alpha: 0.0).cgColor
            ]
            return (primary: primary, secondary: secondary)

        case "Стабильность":
            // Бирюзовый + Розовый (контрастная пара) - более прозрачные
            let primary = [
                NSColor(red: 0.2, green: 0.8, blue: 0.8, alpha: 0.4).cgColor,  // Яркий бирюзовый
                NSColor(red: 0.1, green: 0.4, blue: 0.4, alpha: 0.2).cgColor,
                NSColor(red: 0.05, green: 0.2, blue: 0.2, alpha: 0.0).cgColor
            ]
            let secondary = [
                NSColor(red: 0.9, green: 0.4, blue: 0.7, alpha: 0.35).cgColor,  // Розовый
                NSColor(red: 0.45, green: 0.2, blue: 0.35, alpha: 0.18).cgColor,
                NSColor(red: 0.22, green: 0.1, blue: 0.18, alpha: 0.0).cgColor
            ]
            return (primary: primary, secondary: secondary)

        case "Среднее время начала", "Время начала":
            // Розовый + Зеленый (контрастная пара) - более прозрачные
            let primary = [
                NSColor(red: 0.9, green: 0.4, blue: 0.7, alpha: 0.4).cgColor,  // Яркий розовый
                NSColor(red: 0.45, green: 0.2, blue: 0.35, alpha: 0.2).cgColor,
                NSColor(red: 0.22, green: 0.1, blue: 0.18, alpha: 0.0).cgColor
            ]
            let secondary = [
                NSColor(red: 0.2, green: 0.8, blue: 0.4, alpha: 0.35).cgColor,  // Зеленый
                NSColor(red: 0.1, green: 0.4, blue: 0.2, alpha: 0.18).cgColor,
                NSColor(red: 0.05, green: 0.2, blue: 0.1, alpha: 0.0).cgColor
            ]
            return (primary: primary, secondary: secondary)

        case "Время работы":
            // Красный + Бирюзовый (контрастная пара) - более прозрачные
            let primary = [
                NSColor(red: 0.9, green: 0.3, blue: 0.3, alpha: 0.4).cgColor,  // Яркий красный
                NSColor(red: 0.45, green: 0.15, blue: 0.15, alpha: 0.2).cgColor,
                NSColor(red: 0.22, green: 0.08, blue: 0.08, alpha: 0.0).cgColor
            ]
            let secondary = [
                NSColor(red: 0.2, green: 0.8, blue: 0.8, alpha: 0.35).cgColor,  // Бирюзовый
                NSColor(red: 0.1, green: 0.4, blue: 0.4, alpha: 0.18).cgColor,
                NSColor(red: 0.05, green: 0.2, blue: 0.2, alpha: 0.0).cgColor
            ]
            return (primary: primary, secondary: secondary)

        default:
            // Серый радиальный градиент по умолчанию
            let primary = [
                NSColor(red: 0.5, green: 0.5, blue: 0.6, alpha: 0.4).cgColor,
                NSColor(red: 0.25, green: 0.25, blue: 0.3, alpha: 0.2).cgColor,
                NSColor(red: 0.12, green: 0.12, blue: 0.15, alpha: 0.0).cgColor
            ]
            let secondary = [
                NSColor(red: 0.6, green: 0.6, blue: 0.7, alpha: 0.3).cgColor,
                NSColor(red: 0.3, green: 0.3, blue: 0.35, alpha: 0.15).cgColor,
                NSColor(red: 0.15, green: 0.15, blue: 0.18, alpha: 0.0).cgColor
            ]
            return (primary: primary, secondary: secondary)
        }
    }

    // Функция для получения иконки в зависимости от типа статистики
    private func getIconForLabel(_ label: String) -> String {
        switch label {
        case "Всего интервалов", "Интервалов":
            return "📊"  // График для общего количества
        case "Рабочих дней":
            return "🏛️"  // Здание для рабочих дней
        case "Среднее в день":
            return "↔️"  // Стрелки для среднего значения
        case "Средняя длительность":
            return "⏱️"  // Секундомер для времени
        case "Стабильность":
            return "📈"  // График роста для стабильности
        case "Среднее время начала", "Время начала":
            return "🕐"  // Часы для времени начала
        case "Время работы":
            return "⏰"  // Будильник для общего времени работы
        default:
            return "📋"  // Планшет по умолчанию
        }
    }



    private func createAnalysisRow(risk: RiskFactor?, recommendation: Recommendation?) -> NSView {
        let rowContainer = NSView()
        rowContainer.wantsLayer = true

        // Чистый стиль без фона по умолчанию
        rowContainer.layer?.backgroundColor = NSColor.clear.cgColor

        // Добавляем hover эффект
        let trackingArea = NSTrackingArea(
            rect: rowContainer.bounds,
            options: [.mouseEnteredAndExited, .activeInKeyWindow, .inVisibleRect],
            owner: rowContainer,
            userInfo: nil
        )
        rowContainer.addTrackingArea(trackingArea)

        // Левая колонка - Проблема
        let problemLabel = NSTextField(labelWithString: risk != nil ? formatRiskFactor(risk!) : "—")
        problemLabel.font = NSFont.systemFont(ofSize: 15, weight: .regular)
        problemLabel.textColor = NSColor.white.withAlphaComponent(0.85)
        problemLabel.isBezeled = false
        problemLabel.isEditable = false
        problemLabel.backgroundColor = NSColor.clear
        problemLabel.maximumNumberOfLines = 0
        problemLabel.lineBreakMode = .byWordWrapping

        // Правая колонка - Рекомендация
        let recommendationLabel = NSTextField(labelWithString: recommendation != nil ? formatRecommendation(recommendation!) : "—")
        recommendationLabel.font = NSFont.systemFont(ofSize: 15, weight: .regular)
        recommendationLabel.textColor = NSColor.white.withAlphaComponent(0.85)
        recommendationLabel.isBezeled = false
        recommendationLabel.isEditable = false
        recommendationLabel.backgroundColor = NSColor.clear
        recommendationLabel.maximumNumberOfLines = 0
        recommendationLabel.lineBreakMode = .byWordWrapping

        // Добавляем элементы в контейнер
        rowContainer.addSubview(problemLabel)
        rowContainer.addSubview(recommendationLabel)

        problemLabel.translatesAutoresizingMaskIntoConstraints = false
        recommendationLabel.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            // Проблема - левая колонка (50% ширины)
            problemLabel.leadingAnchor.constraint(equalTo: rowContainer.leadingAnchor, constant: 24),
            problemLabel.topAnchor.constraint(equalTo: rowContainer.topAnchor, constant: 16),
            problemLabel.bottomAnchor.constraint(equalTo: rowContainer.bottomAnchor, constant: -16),
            problemLabel.widthAnchor.constraint(equalTo: rowContainer.widthAnchor, multiplier: 0.48, constant: -36),

            // Рекомендация - правая колонка (50% ширины)
            recommendationLabel.leadingAnchor.constraint(equalTo: problemLabel.trailingAnchor, constant: 24),
            recommendationLabel.trailingAnchor.constraint(equalTo: rowContainer.trailingAnchor, constant: -24),
            recommendationLabel.topAnchor.constraint(equalTo: rowContainer.topAnchor, constant: 16),
            recommendationLabel.bottomAnchor.constraint(equalTo: rowContainer.bottomAnchor, constant: -16),

            // Минимальная высота строки
            rowContainer.heightAnchor.constraint(greaterThanOrEqualToConstant: 56)
        ])

        // Добавляем hover эффект
        let hoverView = HoverTableRowView()
        hoverView.setupRow(container: rowContainer)

        return rowContainer
    }

    private func formatStartTime(_ timeInterval: TimeInterval?) -> String {
        guard let timeInterval = timeInterval else { return "Нет данных" }

        let hours = Int(timeInterval) / 3600
        let minutes = (Int(timeInterval) % 3600) / 60
        return String(format: "%02d:%02d", hours, minutes)
    }

    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        return "\(minutes) мин"
    }

    private func formatRiskFactor(_ risk: RiskFactor) -> String {
        switch risk {
        case .overwork(let averagePerDay):
            if averagePerDay > 7 {
                return "⚠️ Переработка: \(String(format: "%.1f", averagePerDay)) интервалов в день"
            } else {
                return "⚠️ Переработка: слишком интенсивная работа"
            }
        case .longIntervals(let averageDuration):
            let minutes = Int(averageDuration / 60)
            return "⚠️ Переработка: \(minutes) мин интервалов в день"
        case .lateStart(let averageHour):
            return "⚠️ Поздний старт: начало работы в \(averageHour):00"
        case .procrastination(let gapDays):
            return "⚠️ Прокрастинация: \(gapDays) дней без работы"
        case .inconsistency(let score):
            return "⚠️ Непостоянство: стабильность \(Int(score * 100))%"
        case .burnoutRisk(let intenseDays, let restDays):
            return "⚠️ Риск выгорания: \(intenseDays) дней интенсивной работы, \(restDays) дней отдыха"
        }
    }

    private func formatRecommendation(_ recommendation: Recommendation) -> String {
        switch recommendation {
        case .reduceWorkload(let currentAverage, let suggested):
            return "💡 Снизить нагрузку: с \(String(format: "%.1f", currentAverage)) до \(String(format: "%.1f", suggested)) интервалов в день"
        case .increaseConsistency(let suggestion):
            return "💡 Улучшить постоянство: \(suggestion)"
        case .startEarlier(let currentHour, let suggestedHour):
            return "💡 Начинать раньше: рекомендуется \(suggestedHour):00 (сейчас \(currentHour):00)"
        case .takeBreaks(let reason):
            return "💡 Делать перерывы: \(reason)"
        case .establishRoutine(let suggestion):
            return "💡 Установить режим: \(suggestion)"
        case .maintainBalance(let praise):
            return "💡 Поддерживать баланс: \(praise)"
        }
    }

    // MARK: - Навигация по периодам

    @objc private func periodButtonClicked(_ sender: NSButton) {
        let newPeriodType = PeriodType.allCases[sender.tag]
        currentPeriodType = newPeriodType
        currentOffset = 0 // Сбрасываем смещение при смене типа периода

        // Обновляем стили кнопок
        for (index, button) in periodButtons.enumerated() {
            if index == sender.tag {
                button.layer?.backgroundColor = NSColor.white.withAlphaComponent(0.3).cgColor
            } else {
                button.layer?.backgroundColor = NSColor.clear.cgColor
            }
        }

        updatePeriodLabel()
        updateStatistics()
        updateNavigationButtons()
    }

    @objc private func previousPeriod() {
        currentOffset -= 1
        updatePeriodLabel()
        updateStatistics()
        updateNavigationButtons()
    }

    @objc private func nextPeriod() {
        // Проверяем, можем ли мы перейти в будущее
        if canNavigateToNext() {
            currentOffset += 1
            updatePeriodLabel()
            updateStatistics()
        }
        updateNavigationButtons()
    }

    private func updatePeriodLabel() {
        periodLabel.stringValue = getCurrentPeriodText()
    }

    private func canNavigateToNext() -> Bool {
        // Нельзя перейти в будущее дальше текущего периода
        return currentOffset < 0
    }

    private func updateNavigationButtons() {
        nextButton.isEnabled = canNavigateToNext()
        // Кнопка "назад" всегда активна (можно смотреть историю)
        prevButton.isEnabled = true

        // Обновляем внешний вид кнопок
        nextButton.alphaValue = nextButton.isEnabled ? 1.0 : 0.3
        prevButton.alphaValue = prevButton.isEnabled ? 1.0 : 0.3
    }

    private func getCurrentPeriodText() -> String {
        let calendar = Calendar.current
        let now = Date()

        switch currentPeriodType {
        case .day:
            let targetDate = calendar.date(byAdding: .day, value: currentOffset, to: now) ?? now
            let formatter = DateFormatter()
            formatter.dateFormat = "d MMMM yyyy"
            formatter.locale = Locale(identifier: "ru_RU")

            if currentOffset == 0 {
                return "Сегодня (\(formatter.string(from: targetDate)))"
            } else if currentOffset == -1 {
                return "Вчера (\(formatter.string(from: targetDate)))"
            } else if currentOffset < -1 {
                return "\(-currentOffset) дней назад (\(formatter.string(from: targetDate)))"
            } else {
                return "Через \(currentOffset) дней (\(formatter.string(from: targetDate)))"
            }

        case .week:
            guard let weekStart = calendar.dateInterval(of: .weekOfYear, for: now)?.start else {
                return "Текущая неделя"
            }

            let targetWeekStart = calendar.date(byAdding: .weekOfYear, value: currentOffset, to: weekStart) ?? weekStart
            let targetWeekEnd = calendar.date(byAdding: .day, value: 6, to: targetWeekStart) ?? targetWeekStart

            let formatter = DateFormatter()
            formatter.dateFormat = "d MMM"
            formatter.locale = Locale(identifier: "ru_RU")

            let startText = formatter.string(from: targetWeekStart)
            let endText = formatter.string(from: targetWeekEnd)

            if currentOffset == 0 {
                return "Текущая неделя (\(startText) - \(endText))"
            } else if currentOffset == -1 {
                return "Прошлая неделя (\(startText) - \(endText))"
            } else if currentOffset < -1 {
                return "\(-currentOffset) недели назад (\(startText) - \(endText))"
            } else {
                return "Через \(currentOffset) недель (\(startText) - \(endText))"
            }

        case .month:
            let targetDate = calendar.date(byAdding: .month, value: currentOffset, to: now) ?? now
            let formatter = DateFormatter()
            formatter.dateFormat = "LLLL yyyy"
            formatter.locale = Locale(identifier: "ru_RU")

            if currentOffset == 0 {
                return "Текущий месяц (\(formatter.string(from: targetDate)))"
            } else if currentOffset == -1 {
                return "Прошлый месяц (\(formatter.string(from: targetDate)))"
            } else if currentOffset < -1 {
                return "\(-currentOffset) месяцев назад (\(formatter.string(from: targetDate)))"
            } else {
                return "Через \(currentOffset) месяцев (\(formatter.string(from: targetDate)))"
            }

        case .year:
            let targetDate = calendar.date(byAdding: .year, value: currentOffset, to: now) ?? now
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy"

            if currentOffset == 0 {
                return "Текущий год (\(formatter.string(from: targetDate)))"
            } else if currentOffset == -1 {
                return "Прошлый год (\(formatter.string(from: targetDate)))"
            } else if currentOffset < -1 {
                return "\(-currentOffset) лет назад (\(formatter.string(from: targetDate)))"
            } else {
                return "Через \(currentOffset) лет (\(formatter.string(from: targetDate)))"
            }
        }
    }

    // MARK: - Двухколоночный layout

    private func setupTwoColumnLayout(intervals: [(Date, TimeInterval)], pattern: WorkPattern) {
        // Проверяем, что analysisContainer инициализирован
        guard let analysisContainer = analysisContainer else { return }

        // Очищаем analysisContainer, так как будем его переиспользовать
        clearContainer(analysisContainer)

        // Создаем горизонтальный контейнер
        let horizontalContainer = NSView()
        horizontalContainer.wantsLayer = true
        horizontalContainer.layer?.backgroundColor = NSColor.clear.cgColor

        analysisContainer.addSubview(horizontalContainer)
        horizontalContainer.translatesAutoresizingMaskIntoConstraints = false

        // Левая часть - график
        let chartContainer = createWeeklyChartContainer(intervals: intervals)
        horizontalContainer.addSubview(chartContainer)
        chartContainer.translatesAutoresizingMaskIntoConstraints = false

        // Правая часть - анализ и рекомендации
        let analysisRightContainer = createCompactAnalysisContainer(pattern: pattern)
        horizontalContainer.addSubview(analysisRightContainer)
        analysisRightContainer.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            // Горизонтальный контейнер заполняет весь analysisContainer
            horizontalContainer.topAnchor.constraint(equalTo: analysisContainer.topAnchor, constant: 20),
            horizontalContainer.leadingAnchor.constraint(equalTo: analysisContainer.leadingAnchor, constant: 30),
            horizontalContainer.trailingAnchor.constraint(equalTo: analysisContainer.trailingAnchor, constant: -30),
            horizontalContainer.bottomAnchor.constraint(greaterThanOrEqualTo: analysisContainer.bottomAnchor, constant: -20),
            horizontalContainer.heightAnchor.constraint(greaterThanOrEqualToConstant: 300),

            // График слева (40% ширины)
            chartContainer.leadingAnchor.constraint(equalTo: horizontalContainer.leadingAnchor),
            chartContainer.topAnchor.constraint(equalTo: horizontalContainer.topAnchor),
            chartContainer.bottomAnchor.constraint(equalTo: horizontalContainer.bottomAnchor),
            chartContainer.widthAnchor.constraint(equalTo: horizontalContainer.widthAnchor, multiplier: 0.4),

            // Анализ справа (точно 45% ширины) начинается с 55% от левого края
            analysisRightContainer.topAnchor.constraint(equalTo: horizontalContainer.topAnchor),
            analysisRightContainer.bottomAnchor.constraint(lessThanOrEqualTo: horizontalContainer.bottomAnchor),
            analysisRightContainer.widthAnchor.constraint(equalTo: horizontalContainer.widthAnchor, multiplier: 0.45),
            analysisRightContainer.trailingAnchor.constraint(equalTo: horizontalContainer.trailingAnchor)
        ])
    }

    private func createCompactAnalysisContainer(pattern: WorkPattern) -> NSView {
        let container = NSView()
        container.wantsLayer = true
        container.layer?.backgroundColor = NSColor.clear.cgColor

        // Заголовок
        let titleLabel = NSTextField(labelWithString: "🔍 Анализ и рекомендации")
        titleLabel.font = NSFont.systemFont(ofSize: 16, weight: .bold)
        titleLabel.textColor = NSColor.white
        titleLabel.isBezeled = false
        titleLabel.isEditable = false
        titleLabel.backgroundColor = NSColor.clear

        container.addSubview(titleLabel)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false

        var lastView: NSView = titleLabel

        // Создаем вертикальный список: проблема -> рекомендация
        let allItems = max(pattern.riskFactors.count, pattern.recommendations.count)

        if allItems > 0 {
            for i in 0..<allItems {
                if i < pattern.riskFactors.count {
                    // Добавляем риск
                    let riskView = createCompactRiskView(risk: pattern.riskFactors[i])
                    container.addSubview(riskView)
                    riskView.translatesAutoresizingMaskIntoConstraints = false

                    NSLayoutConstraint.activate([
                        riskView.topAnchor.constraint(equalTo: lastView.bottomAnchor, constant: i == 0 ? 15 : 10),
                        riskView.leadingAnchor.constraint(equalTo: container.leadingAnchor),
                        riskView.trailingAnchor.constraint(equalTo: container.trailingAnchor)
                    ])

                    lastView = riskView
                }

                if i < pattern.recommendations.count {
                    // Добавляем рекомендацию под риском
                    let recommendationView = createCompactRecommendationView(recommendation: pattern.recommendations[i])
                    container.addSubview(recommendationView)
                    recommendationView.translatesAutoresizingMaskIntoConstraints = false

                    NSLayoutConstraint.activate([
                        recommendationView.topAnchor.constraint(equalTo: lastView.bottomAnchor, constant: 5),
                        recommendationView.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 15),
                        recommendationView.trailingAnchor.constraint(equalTo: container.trailingAnchor)
                    ])

                    lastView = recommendationView
                }
            }
        } else {
            // Если нет рисков и рекомендаций
            let noDataLabel = NSTextField(labelWithString: "✨ Отличная работа!")
            noDataLabel.font = NSFont.systemFont(ofSize: 14, weight: .medium)
            noDataLabel.textColor = NSColor.white.withAlphaComponent(0.7)
            noDataLabel.isBezeled = false
            noDataLabel.isEditable = false
            noDataLabel.backgroundColor = NSColor.clear

            container.addSubview(noDataLabel)
            noDataLabel.translatesAutoresizingMaskIntoConstraints = false

            NSLayoutConstraint.activate([
                noDataLabel.topAnchor.constraint(equalTo: lastView.bottomAnchor, constant: 15),
                noDataLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor),
                noDataLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor)
            ])
        }

        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: container.topAnchor),
            titleLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            titleLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor)
        ])

        return container
    }

    private func createCompactRiskView(risk: RiskFactor) -> NSView {
        let container = NSView()
        container.wantsLayer = true
        container.layer?.backgroundColor = NSColor.white.withAlphaComponent(0.08).cgColor
        container.layer?.cornerRadius = 6

        let label = NSTextField(labelWithString: formatRiskFactor(risk))
        label.font = NSFont.systemFont(ofSize: 13, weight: .medium)
        label.textColor = NSColor.white.withAlphaComponent(0.85)
        label.isBezeled = false
        label.isEditable = false
        label.backgroundColor = NSColor.clear
        label.lineBreakMode = .byWordWrapping
        label.maximumNumberOfLines = 0

        container.addSubview(label)
        label.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            label.topAnchor.constraint(equalTo: container.topAnchor, constant: 8),
            label.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 10),
            label.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -10),
            label.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -8)
        ])

        return container
    }

    private func createCompactRecommendationView(recommendation: Recommendation) -> NSView {
        let container = NSView()
        container.wantsLayer = true
        container.layer?.backgroundColor = NSColor.white.withAlphaComponent(0.06).cgColor
        container.layer?.cornerRadius = 6

        let label = NSTextField(labelWithString: formatRecommendation(recommendation))
        label.font = NSFont.systemFont(ofSize: 13, weight: .regular)
        label.textColor = NSColor.white.withAlphaComponent(0.8)
        label.isBezeled = false
        label.isEditable = false
        label.backgroundColor = NSColor.clear
        label.lineBreakMode = .byWordWrapping
        label.maximumNumberOfLines = 0

        container.addSubview(label)
        label.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            label.topAnchor.constraint(equalTo: container.topAnchor, constant: 8),
            label.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 10),
            label.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -10),
            label.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -8)
        ])

        return container
    }

    // MARK: - График по дням недели

    private func createWeeklyChartContainer(intervals: [(Date, TimeInterval)]) -> NSView {
        let analysisPeriod = getCurrentAnalysisPeriod()
        let chartData = analyzer.getWeeklyChartData(for: analysisPeriod)

        let container = NSView()
        container.wantsLayer = true
        container.layer?.backgroundColor = NSColor.clear.cgColor

        // Заголовок
        let titleLabel = NSTextField(labelWithString: "📊 Интервалы по дням недели")
        titleLabel.font = NSFont.systemFont(ofSize: 16, weight: .semibold)
        titleLabel.textColor = NSColor.white
        titleLabel.isBezeled = false
        titleLabel.isEditable = false
        titleLabel.backgroundColor = NSColor.clear

        container.addSubview(titleLabel)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false

        // График
        let chartView = createWeeklyChartView(data: chartData)
        container.addSubview(chartView)
        chartView.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: container.topAnchor),
            titleLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            titleLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor),

            chartView.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 15),
            chartView.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            chartView.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            chartView.bottomAnchor.constraint(equalTo: container.bottomAnchor),
            chartView.heightAnchor.constraint(greaterThanOrEqualToConstant: 200)
        ])

        return container
    }

    private func addWeeklyChart(intervals: [(Date, TimeInterval)]) {
        // Проверяем, что statsContainer инициализирован
        guard let statsContainer = statsContainer else { return }

        let analysisPeriod = getCurrentAnalysisPeriod()
        let chartData = analyzer.getWeeklyChartData(for: analysisPeriod)

        // Создаем контейнер для графика
        let chartContainer = createTransparentContainer()
        statsContainer.addSubview(chartContainer)
        chartContainer.translatesAutoresizingMaskIntoConstraints = false

        // Добавляем заголовок
        let titleLabel = NSTextField(labelWithString: "📊 Интервалы по дням недели")
        titleLabel.font = NSFont.systemFont(ofSize: 16, weight: .semibold)
        titleLabel.textColor = NSColor.white
        titleLabel.isBezeled = false
        titleLabel.isEditable = false
        titleLabel.backgroundColor = NSColor.clear

        chartContainer.addSubview(titleLabel)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false

        // Создаем график
        let chartView = createWeeklyChartView(data: chartData)
        chartContainer.addSubview(chartView)
        chartView.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            // Позиционирование контейнера графика
            chartContainer.topAnchor.constraint(equalTo: statsContainer.bottomAnchor, constant: 20),
            chartContainer.leadingAnchor.constraint(equalTo: statsContainer.leadingAnchor),
            chartContainer.trailingAnchor.constraint(equalTo: statsContainer.trailingAnchor),
            chartContainer.heightAnchor.constraint(equalToConstant: 200),

            // Заголовок
            titleLabel.topAnchor.constraint(equalTo: chartContainer.topAnchor, constant: 20),
            titleLabel.leadingAnchor.constraint(equalTo: chartContainer.leadingAnchor, constant: 60),

            // График
            chartView.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 15),
            chartView.leadingAnchor.constraint(equalTo: chartContainer.leadingAnchor, constant: 60),
            chartView.trailingAnchor.constraint(equalTo: chartContainer.trailingAnchor, constant: -60),
            chartView.bottomAnchor.constraint(equalTo: chartContainer.bottomAnchor, constant: -20)
        ])
    }

    private func createWeeklyChartView(data: [DayChartData]) -> NSView {
        let chartView = NSView()
        chartView.wantsLayer = true
        chartView.layer?.backgroundColor = NSColor.clear.cgColor

        // Находим максимальное количество интервалов для масштабирования
        let maxIntervals = max(data.map { $0.totalIntervals }.max() ?? 1, 8) // Минимум 8 для красивого масштаба
        let chartHeight: CGFloat = 120
        let yAxisWidth: CGFloat = 40

        // Создаем ось Y
        let yAxisContainer = createYAxis(maxIntervals: maxIntervals, chartHeight: chartHeight)
        chartView.addSubview(yAxisContainer)
        yAxisContainer.translatesAutoresizingMaskIntoConstraints = false

        // Создаем столбцы для каждого дня
        for (index, dayData) in data.enumerated() {
            let dayContainer = createDayColumn(dayData: dayData, maxIntervals: maxIntervals, chartHeight: chartHeight)
            chartView.addSubview(dayContainer)
            dayContainer.translatesAutoresizingMaskIntoConstraints = false

            let columnWidth: CGFloat = 55
            let spacing: CGFloat = 10
            let leadingOffset = yAxisWidth + 10 + CGFloat(index) * (columnWidth + spacing)

            NSLayoutConstraint.activate([
                dayContainer.leadingAnchor.constraint(equalTo: chartView.leadingAnchor, constant: leadingOffset),
                dayContainer.topAnchor.constraint(equalTo: chartView.topAnchor),
                dayContainer.widthAnchor.constraint(equalToConstant: columnWidth),
                dayContainer.heightAnchor.constraint(equalToConstant: chartHeight + 30) // +30 для подписи
            ])
        }

        NSLayoutConstraint.activate([
            yAxisContainer.leadingAnchor.constraint(equalTo: chartView.leadingAnchor),
            yAxisContainer.topAnchor.constraint(equalTo: chartView.topAnchor),
            yAxisContainer.widthAnchor.constraint(equalToConstant: yAxisWidth),
            yAxisContainer.heightAnchor.constraint(equalToConstant: chartHeight + 30)
        ])

        return chartView
    }

    private func createYAxis(maxIntervals: Int, chartHeight: CGFloat) -> NSView {
        let container = NSView()
        container.wantsLayer = true
        container.layer?.backgroundColor = NSColor.clear.cgColor

        // Создаем метки для оси Y
        let steps = min(maxIntervals, 8) // Максимум 8 меток
        let stepValue = maxIntervals / steps

        for i in 0...steps {
            let value = i * stepValue
            let label = NSTextField(labelWithString: "\(value)")
            label.font = NSFont.systemFont(ofSize: 10, weight: .regular)
            label.textColor = NSColor.lightGray
            label.isBezeled = false
            label.isEditable = false
            label.backgroundColor = NSColor.clear
            label.alignment = .right

            container.addSubview(label)
            label.translatesAutoresizingMaskIntoConstraints = false

            let yPosition = chartHeight - (CGFloat(i) / CGFloat(steps) * chartHeight)

            NSLayoutConstraint.activate([
                label.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -5),
                label.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -30 - yPosition),
                label.widthAnchor.constraint(equalToConstant: 30)
            ])
        }

        return container
    }

    private func createDayColumn(dayData: DayChartData, maxIntervals: Int, chartHeight: CGFloat) -> NSView {
        let container = NSView()
        container.wantsLayer = true
        container.layer?.backgroundColor = NSColor.clear.cgColor

        // Подпись дня
        let dayLabel = NSTextField(labelWithString: dayData.dayName)
        dayLabel.font = NSFont.systemFont(ofSize: 12, weight: .medium)
        dayLabel.textColor = NSColor.lightGray
        dayLabel.isBezeled = false
        dayLabel.isEditable = false
        dayLabel.backgroundColor = NSColor.clear
        dayLabel.alignment = .center

        container.addSubview(dayLabel)
        dayLabel.translatesAutoresizingMaskIntoConstraints = false

        // Создаем единый столбец с цветом в зависимости от количества интервалов
        if dayData.totalIntervals > 0 {
            let totalHeight = CGFloat(dayData.totalIntervals) / CGFloat(maxIntervals) * chartHeight
            let barColor = getColorForIntervals(dayData.totalIntervals)
            let bar = createBar(height: totalHeight, color: barColor)
            container.addSubview(bar)
            bar.translatesAutoresizingMaskIntoConstraints = false

            NSLayoutConstraint.activate([
                bar.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 8),
                bar.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -8),
                bar.bottomAnchor.constraint(equalTo: dayLabel.topAnchor, constant: -5),
                bar.heightAnchor.constraint(equalToConstant: totalHeight)
            ])
        }

        // Подпись с количеством
        if dayData.totalIntervals > 0 {
            let countLabel = NSTextField(labelWithString: "\(dayData.totalIntervals)")
            countLabel.font = NSFont.systemFont(ofSize: 10, weight: .medium)
            countLabel.textColor = NSColor.white
            countLabel.isBezeled = false
            countLabel.isEditable = false
            countLabel.backgroundColor = NSColor.clear
            countLabel.alignment = .center

            container.addSubview(countLabel)
            countLabel.translatesAutoresizingMaskIntoConstraints = false

            NSLayoutConstraint.activate([
                countLabel.centerXAnchor.constraint(equalTo: container.centerXAnchor),
                countLabel.bottomAnchor.constraint(equalTo: dayLabel.topAnchor, constant: -10)
            ])
        }

        NSLayoutConstraint.activate([
            dayLabel.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            dayLabel.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            dayLabel.bottomAnchor.constraint(equalTo: container.bottomAnchor)
        ])

        return container
    }

    private func getColorForIntervals(_ intervals: Int) -> NSColor {
        switch intervals {
        case 1...2:
            return NSColor.systemBlue      // 1-2 - синий
        case 3...5:
            return NSColor.systemGreen     // 3-5 - зеленый
        case 6:
            return NSColor.systemYellow    // 6 - желтый
        case 7:
            return NSColor.systemOrange    // 7 - оранжевый
        case 8...:
            return NSColor.systemRed       // 8+ - красный
        default:
            return NSColor.lightGray       // 0 - серый
        }
    }

    private func createBar(height: CGFloat, color: NSColor) -> NSView {
        let bar = NSView()
        bar.wantsLayer = true

        // Создаем кастомный view с градиентом
        let gradientView = GradientBarView(color: color)
        bar.addSubview(gradientView)
        gradientView.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            gradientView.leadingAnchor.constraint(equalTo: bar.leadingAnchor),
            gradientView.trailingAnchor.constraint(equalTo: bar.trailingAnchor),
            gradientView.topAnchor.constraint(equalTo: bar.topAnchor),
            gradientView.bottomAnchor.constraint(equalTo: bar.bottomAnchor)
        ])

        bar.layer?.cornerRadius = 4

        return bar
    }
}

// MARK: - NSWindowDelegate

extension StatisticsWindow: NSWindowDelegate {
    func windowWillClose(_ notification: Notification) {
        // Окно просто скрывается, не освобождается
    }

    @objc func windowDidResize(_ notification: Notification) {
        updateGradientFrame()
        updateStatCardGradients()
    }

    private func updateStatCardGradients() {
        // Обновляем градиенты во всех статистических карточках
        guard let statsContainer = statsContainer else { return }

        for subview in statsContainer.subviews {
            if let gradientLayer = subview.layer?.sublayers?.first as? CAGradientLayer {
                DispatchQueue.main.async {
                    gradientLayer.frame = subview.bounds
                }
            }
        }
    }
}

// MARK: - GradientBarView

class GradientBarView: NSView {
    private let baseColor: NSColor
    private var gradientLayer: CAGradientLayer?

    init(color: NSColor) {
        self.baseColor = color
        super.init(frame: .zero)
        setupGradient()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupGradient() {
        wantsLayer = true

        let gradientLayer = CAGradientLayer()

        // Светлый цвет сверху (исходный цвет)
        let lightColor = baseColor
        // Темный цвет снизу (затемняем исходный цвет)
        let darkColor = baseColor.blended(withFraction: 0.3, of: NSColor.black) ?? baseColor

        gradientLayer.colors = [lightColor.cgColor, darkColor.cgColor]
        gradientLayer.startPoint = CGPoint(x: 0.5, y: 0.0) // Сверху
        gradientLayer.endPoint = CGPoint(x: 0.5, y: 1.0)   // Снизу
        gradientLayer.cornerRadius = 4

        layer?.addSublayer(gradientLayer)
        layer?.cornerRadius = 4

        self.gradientLayer = gradientLayer
    }

    override func layout() {
        super.layout()
        gradientLayer?.frame = bounds
    }


}


